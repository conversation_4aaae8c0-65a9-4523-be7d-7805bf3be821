#!/usr/bin/env python3
"""
USDC对清算驱动Lead-Lag套利策略
基于USDT对成功经验，适配USDC交易对：
1. Binance USDC永续合约
2. Bybit USDC永续合约
3. 优化的清算跟随逻辑
4. 增强的风险管理
"""

import pandas as pd
import numpy as np
import asyncio
import time
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
import logging
from collections import deque

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class MarketData:
    """市场数据结构"""
    timestamp: datetime
    binance_bid: float
    binance_ask: float
    bybit_bid: float
    bybit_ask: float
    binance_mid: float
    bybit_mid: float

@dataclass
class LiquidationEvent:
    """清算事件结构"""
    timestamp: datetime
    side: str  # BUY/SELL
    price: float
    quantity: float
    avg_price: float

@dataclass
class TradingSignal:
    """交易信号结构"""
    timestamp: datetime
    action: str  # BUY/SELL
    exchange: str  # binance/bybit
    price: float
    quantity: float
    confidence: float
    reason: str

class USDCLiquidationLeadLagStrategy:
    """USDC对清算驱动Lead-Lag策略"""
    
    def __init__(self, config: Dict):
        """初始化策略"""
        self.config = config
        
        # 策略参数 - 针对USDC对优化
        self.symbol_usdt = config.get('symbol_usdt', 'BTCUSDT')  # Binance USDT对
        self.symbol_usdc = config.get('symbol_usdc', 'BTCUSDC')  # Binance USDC对
        self.bybit_symbol = config.get('bybit_symbol', 'BTCUSDC')  # Bybit USDC对
        
        # 清算跟随参数
        self.liquidation_threshold = config.get('liquidation_threshold', 50000)  # USDC
        self.follow_delay_ms = config.get('follow_delay_ms', 100)
        self.max_position_usdc = config.get('max_position_usdc', 10000)
        
        # 价差参数 - USDC对通常价差更小
        self.min_spread_bps = config.get('min_spread_bps', 2)  # 2个基点
        self.max_spread_bps = config.get('max_spread_bps', 50)  # 50个基点
        
        # 风险管理
        self.stop_loss_pct = config.get('stop_loss_pct', 0.5)  # 0.5%
        self.take_profit_pct = config.get('take_profit_pct', 0.3)  # 0.3%
        self.max_holding_time = config.get('max_holding_time', 300)  # 5分钟
        
        # 数据存储
        self.market_data_history = deque(maxlen=1000)
        self.liquidation_history = deque(maxlen=100)
        self.signal_history = deque(maxlen=50)
        self.positions = {}
        
        # 统计信息
        self.stats = {
            'total_signals': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_pnl_usdc': 0.0,
            'max_drawdown': 0.0,
            'win_rate': 0.0
        }
        
        logger.info(f"USDC Lead-Lag策略初始化完成")
        logger.info(f"交易对: {self.symbol_usdc} (Binance) <-> {self.bybit_symbol} (Bybit)")
        logger.info(f"清算阈值: {self.liquidation_threshold} USDC")

    def process_liquidation_event(self, liquidation: LiquidationEvent) -> Optional[TradingSignal]:
        """处理清算事件，生成交易信号"""
        try:
            # 记录清算事件
            self.liquidation_history.append(liquidation)
            
            # 检查清算规模
            liquidation_value = liquidation.price * liquidation.quantity
            if liquidation_value < self.liquidation_threshold:
                return None
            
            # 获取最新市场数据
            if not self.market_data_history:
                return None
            
            latest_market = self.market_data_history[-1]
            
            # 计算当前价差
            spread_bps = self._calculate_spread_bps(latest_market)
            if not self._is_spread_valid(spread_bps):
                return None
            
            # 生成反向跟随信号 (基于USDT对成功经验)
            signal = self._generate_reverse_follow_signal(liquidation, latest_market, spread_bps)
            
            if signal:
                self.signal_history.append(signal)
                self.stats['total_signals'] += 1
                logger.info(f"生成USDC交易信号: {signal.action} @ {signal.price:.4f}")
            
            return signal
            
        except Exception as e:
            logger.error(f"处理清算事件失败: {e}")
            return None

    def _generate_reverse_follow_signal(self, liquidation: LiquidationEvent, 
                                      market: MarketData, spread_bps: float) -> Optional[TradingSignal]:
        """生成反向跟随信号 - 基于USDT对验证的逻辑"""
        try:
            # 反向跟随逻辑：多单清算→做多，空单清算→做空
            if liquidation.side == 'SELL':  # 多单被清算
                # 预期价格反弹，在Bybit做多
                action = 'BUY'
                target_price = market.bybit_ask * 0.9995  # 稍低于ask价
                exchange = 'bybit'
                reason = f"多单清算{liquidation.quantity:.0f}，预期反弹"
            else:  # 空单被清算
                # 预期价格回调，在Bybit做空
                action = 'SELL'
                target_price = market.bybit_bid * 1.0005  # 稍高于bid价
                exchange = 'bybit'
                reason = f"空单清算{liquidation.quantity:.0f}，预期回调"
            
            # 计算仓位大小 - 基于清算价值的比例
            liquidation_value = liquidation.price * liquidation.quantity
            position_ratio = min(0.1, liquidation_value / 100000)  # 最多10%
            quantity = min(
                position_ratio * liquidation.quantity,
                self.max_position_usdc / target_price
            )
            
            # 计算信心度
            confidence = self._calculate_confidence(liquidation, market, spread_bps)
            
            if confidence < 0.6:  # 信心度阈值
                return None
            
            return TradingSignal(
                timestamp=datetime.now(),
                action=action,
                exchange=exchange,
                price=target_price,
                quantity=quantity,
                confidence=confidence,
                reason=reason
            )
            
        except Exception as e:
            logger.error(f"生成信号失败: {e}")
            return None

    def _calculate_confidence(self, liquidation: LiquidationEvent, 
                            market: MarketData, spread_bps: float) -> float:
        """计算信号信心度"""
        confidence = 0.5  # 基础信心度
        
        # 基于清算规模
        liquidation_value = liquidation.price * liquidation.quantity
        if liquidation_value > 100000:  # 大额清算
            confidence += 0.2
        elif liquidation_value > 50000:  # 中等清算
            confidence += 0.1
        
        # 基于价差
        if 5 <= spread_bps <= 20:  # 理想价差范围
            confidence += 0.2
        elif spread_bps > 30:  # 价差过大
            confidence -= 0.1
        
        # 基于历史成功率
        if self.stats['total_signals'] > 10:
            win_rate = self.stats['successful_trades'] / self.stats['total_signals']
            if win_rate > 0.6:
                confidence += 0.1
            elif win_rate < 0.4:
                confidence -= 0.1
        
        return min(1.0, max(0.0, confidence))

    def _calculate_spread_bps(self, market: MarketData) -> float:
        """计算价差（基点）"""
        try:
            # 使用中间价计算价差
            spread = abs(market.binance_mid - market.bybit_mid)
            avg_price = (market.binance_mid + market.bybit_mid) / 2
            spread_bps = (spread / avg_price) * 10000
            return spread_bps
        except:
            return 0.0

    def _is_spread_valid(self, spread_bps: float) -> bool:
        """检查价差是否在有效范围内"""
        return self.min_spread_bps <= spread_bps <= self.max_spread_bps

    def update_market_data(self, binance_data: Dict, bybit_data: Dict):
        """更新市场数据"""
        try:
            market_data = MarketData(
                timestamp=datetime.now(),
                binance_bid=float(binance_data.get('bid', 0)),
                binance_ask=float(binance_data.get('ask', 0)),
                bybit_bid=float(bybit_data.get('bid', 0)),
                bybit_ask=float(bybit_data.get('ask', 0)),
                binance_mid=(float(binance_data.get('bid', 0)) + float(binance_data.get('ask', 0))) / 2,
                bybit_mid=(float(bybit_data.get('bid', 0)) + float(bybit_data.get('ask', 0))) / 2
            )
            
            self.market_data_history.append(market_data)
            
        except Exception as e:
            logger.error(f"更新市场数据失败: {e}")

    def execute_signal(self, signal: TradingSignal) -> bool:
        """执行交易信号"""
        try:
            # 这里应该连接到实际的交易API
            # 目前只是模拟执行
            
            position_id = f"{signal.exchange}_{signal.action}_{int(time.time())}"
            
            self.positions[position_id] = {
                'signal': signal,
                'entry_time': datetime.now(),
                'entry_price': signal.price,
                'quantity': signal.quantity,
                'status': 'open'
            }
            
            logger.info(f"执行USDC信号: {signal.action} {signal.quantity:.4f} @ {signal.price:.4f}")
            return True
            
        except Exception as e:
            logger.error(f"执行信号失败: {e}")
            return False

    def check_positions(self):
        """检查持仓状态"""
        current_time = datetime.now()
        
        for position_id, position in list(self.positions.items()):
            if position['status'] != 'open':
                continue
            
            # 检查持仓时间
            holding_time = (current_time - position['entry_time']).total_seconds()
            if holding_time > self.max_holding_time:
                self._close_position(position_id, "超时平仓")
                continue
            
            # 检查止盈止损
            if self.market_data_history:
                latest_market = self.market_data_history[-1]
                self._check_stop_conditions(position_id, position, latest_market)

    def _close_position(self, position_id: str, reason: str):
        """平仓"""
        try:
            position = self.positions[position_id]
            position['status'] = 'closed'
            position['close_time'] = datetime.now()
            position['close_reason'] = reason
            
            # 计算PnL (简化计算)
            if self.market_data_history:
                latest_market = self.market_data_history[-1]
                if position['signal'].exchange == 'bybit':
                    current_price = latest_market.bybit_mid
                else:
                    current_price = latest_market.binance_mid
                
                if position['signal'].action == 'BUY':
                    pnl = (current_price - position['entry_price']) * position['quantity']
                else:
                    pnl = (position['entry_price'] - current_price) * position['quantity']
                
                position['pnl'] = pnl
                self.stats['total_pnl_usdc'] += pnl
                
                if pnl > 0:
                    self.stats['successful_trades'] += 1
                else:
                    self.stats['failed_trades'] += 1
                
                # 更新胜率
                total_trades = self.stats['successful_trades'] + self.stats['failed_trades']
                if total_trades > 0:
                    self.stats['win_rate'] = self.stats['successful_trades'] / total_trades
                
                logger.info(f"平仓: {position_id}, PnL: {pnl:.4f} USDC, 原因: {reason}")
            
        except Exception as e:
            logger.error(f"平仓失败: {e}")

    def _check_stop_conditions(self, position_id: str, position: Dict, market: MarketData):
        """检查止盈止损条件"""
        try:
            if position['signal'].exchange == 'bybit':
                current_price = market.bybit_mid
            else:
                current_price = market.binance_mid
            
            entry_price = position['entry_price']
            
            if position['signal'].action == 'BUY':
                pnl_pct = (current_price - entry_price) / entry_price
            else:
                pnl_pct = (entry_price - current_price) / entry_price
            
            # 止盈
            if pnl_pct >= self.take_profit_pct / 100:
                self._close_position(position_id, f"止盈 {pnl_pct:.2%}")
            # 止损
            elif pnl_pct <= -self.stop_loss_pct / 100:
                self._close_position(position_id, f"止损 {pnl_pct:.2%}")
                
        except Exception as e:
            logger.error(f"检查止盈止损失败: {e}")

    def get_stats(self) -> Dict:
        """获取策略统计信息"""
        return {
            **self.stats,
            'open_positions': len([p for p in self.positions.values() if p['status'] == 'open']),
            'total_positions': len(self.positions),
            'avg_pnl_per_trade': self.stats['total_pnl_usdc'] / max(1, self.stats['successful_trades'] + self.stats['failed_trades'])
        }

# 示例配置
DEFAULT_USDC_CONFIG = {
    'symbol_usdt': 'BTCUSDT',
    'symbol_usdc': 'BTCUSDC', 
    'bybit_symbol': 'BTCUSDC',
    'liquidation_threshold': 50000,  # USDC
    'follow_delay_ms': 100,
    'max_position_usdc': 10000,
    'min_spread_bps': 2,
    'max_spread_bps': 50,
    'stop_loss_pct': 0.5,
    'take_profit_pct': 0.3,
    'max_holding_time': 300
}
