
import subprocess
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import glob
import pyarrow.ipc as ipc
import pyarrow.parquet as pq
from custom_data import BinanceFuturesLiquidationOrder
from nautilus_trader.serialization.arrow.serializer import ArrowSerializer, register_arrow
from binance_data_loader import TradesLoader as BinanceTradesLoader
from bybit_data_loader import TradesLoader as BybitTradesLoader
import argparse
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.signal import find_peaks
from statsmodels.tsa.stattools import grangercausalitytests, coint
from statsmodels.tsa.vector_ar.var_model import VAR
from statsmodels.stats.diagnostic import acorr_ljungbox
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

register_arrow(
    BinanceFuturesLiquidationOrder,
    BinanceFuturesLiquidationOrder.schema(),
    BinanceFuturesLiquidationOrder.to_catalog,
    BinanceFuturesLiquidationOrder.from_catalog,
)

# ==================== 事件驱动分析框架 ====================

class LiquidationEventDetector:
    """
    清算事件检测器 - 基于专家资料的事件驱动分析

    功能：
    1. 检测异常成交量和价格跳跃
    2. 分类清算事件类型和强度
    3. 动态阈值设置
    4. 市场状态识别
    """

    def __init__(self, volume_threshold_multiplier=3.0, price_jump_threshold=0.05):
        self.volume_threshold_multiplier = volume_threshold_multiplier
        self.price_jump_threshold = price_jump_threshold
        self.isolation_forest = IsolationForest(contamination=0.1, random_state=42)
        self.scaler = StandardScaler()

    def detect_volume_anomaly(self, volume_data, window=24):
        """检测成交量异常 - 基于滑动窗口统计"""
        rolling_mean = volume_data.rolling(window).mean()
        rolling_std = volume_data.rolling(window).std()

        # 动态阈值：均值 + N倍标准差
        threshold = rolling_mean + self.volume_threshold_multiplier * rolling_std
        anomalies = volume_data > threshold

        return anomalies, threshold

    def detect_price_jump(self, price_data, method='returns'):
        """检测价格跳跃事件"""
        if method == 'returns':
            returns = price_data.pct_change().abs()
            jumps = returns > self.price_jump_threshold
        elif method == 'gap':
            # 检测价格缺口
            gaps = (price_data - price_data.shift(1)).abs() / price_data.shift(1)
            jumps = gaps > self.price_jump_threshold

        return jumps

    def classify_market_state(self, price_data, volume_data, window=60):
        """
        市场状态分类

        状态类型：
        - 高波动期 (high_volatility)
        - 低波动期 (low_volatility)
        - 高流动性期 (high_liquidity)
        - 低流动性期 (low_liquidity)
        - 异常期 (anomaly)
        """
        # 计算波动率
        returns = price_data.pct_change()
        volatility = returns.rolling(window).std()
        vol_percentile = volatility.rolling(window*5).rank(pct=True)

        # 计算流动性指标（基于成交量）
        volume_ma = volume_data.rolling(window).mean()
        liquidity_percentile = volume_ma.rolling(window*5).rank(pct=True)

        # 状态分类
        states = pd.Series(index=price_data.index, dtype='object')

        # 高波动期
        states[vol_percentile > 0.8] = 'high_volatility'
        # 低波动期
        states[vol_percentile < 0.2] = 'low_volatility'
        # 高流动性期
        states[(vol_percentile >= 0.2) & (vol_percentile <= 0.8) & (liquidity_percentile > 0.7)] = 'high_liquidity'
        # 低流动性期
        states[(vol_percentile >= 0.2) & (vol_percentile <= 0.8) & (liquidity_percentile < 0.3)] = 'low_liquidity'
        # 正常期
        states[states.isna()] = 'normal'

        return states

    def detect_liquidation_clusters(self, liquidation_events, time_threshold_minutes=5):
        """检测清算事件聚集"""
        if len(liquidation_events) < 2:
            return pd.Series(False, index=liquidation_events.index)

        # 计算事件间时间差
        time_diffs = liquidation_events['timestamp'].diff()

        # 标记聚集事件（时间间隔小于阈值）
        clusters = time_diffs < timedelta(minutes=time_threshold_minutes)

        return clusters

class MarketStateAnalyzer:
    """
    市场状态分析器 - 条件化Leader-Lag分析
    """

    def __init__(self):
        self.state_relationships = {}

    def conditional_leader_lag_analysis(self, price_data_leader, price_data_follower,
                                      market_states, min_samples=30):
        """
        条件化Leader-Lag分析 - 基于市场状态分组
        """
        results = {}

        # 按市场状态分组
        for state in market_states.unique():
            if pd.isna(state):
                continue

            state_mask = market_states == state
            state_data_leader = price_data_leader[state_mask]
            state_data_follower = price_data_follower[state_mask]

            if len(state_data_leader) < min_samples:
                continue

            # 计算该状态下的Leader-Lag关系
            lags, correlations = calculate_cross_correlation_efficient(
                state_data_leader.values,
                state_data_follower.values
            )

            if len(correlations) > 0:
                max_corr_idx = np.argmax(np.abs(correlations))
                max_correlation = correlations[max_corr_idx]
                optimal_lag = lags[max_corr_idx]

                # 统计显著性检验
                significance = self.test_correlation_significance(
                    state_data_leader, state_data_follower, optimal_lag
                )

                results[state] = {
                    'correlation': max_correlation,
                    'lag_time': optimal_lag,
                    'significance': significance,
                    'sample_size': len(state_data_leader),
                    'abs_correlation': abs(max_correlation)
                }

        return results

    def test_correlation_significance(self, x, y, lag=0, alpha=0.05):
        """测试相关性的统计显著性"""
        try:
            if lag != 0:
                if lag > 0:
                    x_aligned = x[:-lag]
                    y_aligned = y[lag:]
                else:
                    x_aligned = x[-lag:]
                    y_aligned = y[:lag]
            else:
                x_aligned = x
                y_aligned = y

            # 确保数据长度一致且有效
            valid_idx = ~(pd.isna(x_aligned) | pd.isna(y_aligned))
            if valid_idx.sum() < 10:
                return {'significant': False, 'p_value': 1.0, 't_stat': 0.0}

            x_clean = x_aligned[valid_idx]
            y_clean = y_aligned[valid_idx]

            # 计算相关系数和t统计量
            correlation, p_value = stats.pearsonr(x_clean, y_clean)
            n = len(x_clean)
            t_stat = correlation * np.sqrt((n - 2) / (1 - correlation**2))

            return {
                'significant': p_value < alpha,
                'p_value': p_value,
                't_stat': t_stat,
                'correlation': correlation,
                'sample_size': n
            }
        except Exception as e:
            return {'significant': False, 'p_value': 1.0, 't_stat': 0.0, 'error': str(e)}

class AdvancedStatisticalAnalyzer:
    """
    高级统计分析器 - 实现专家资料中的高级分析方法

    功能：
    1. 格兰杰因果检验
    2. 向量自回归(VAR)模型
    3. 协整检验
    4. 动态相关性分析
    5. 时变参数模型
    """

    def __init__(self):
        self.var_model = None
        self.cointegration_results = None

    def granger_causality_test(self, leader_series, follower_series, max_lags=10):
        """
        格兰杰因果检验 - 确定因果关系方向

        Returns:
            dict: 包含双向因果检验结果
        """
        try:
            # 准备数据
            data = pd.DataFrame({
                'leader': leader_series,
                'follower': follower_series
            }).dropna()

            if len(data) < max_lags * 3:
                return {'error': 'Insufficient data for Granger causality test'}

            # 测试 leader -> follower
            try:
                gc_leader_to_follower = grangercausalitytests(
                    data[['follower', 'leader']], max_lags, verbose=False
                )

                # 提取最佳滞后期的p值
                best_lag_lf = min(gc_leader_to_follower.keys(),
                                key=lambda x: gc_leader_to_follower[x][0]['ssr_ftest'][1])
                p_value_lf = gc_leader_to_follower[best_lag_lf][0]['ssr_ftest'][1]

            except Exception as e:
                p_value_lf = 1.0
                best_lag_lf = 1

            # 测试 follower -> leader
            try:
                gc_follower_to_leader = grangercausalitytests(
                    data[['leader', 'follower']], max_lags, verbose=False
                )

                best_lag_fl = min(gc_follower_to_leader.keys(),
                                key=lambda x: gc_follower_to_leader[x][0]['ssr_ftest'][1])
                p_value_fl = gc_follower_to_leader[best_lag_fl][0]['ssr_ftest'][1]

            except Exception as e:
                p_value_fl = 1.0
                best_lag_fl = 1

            # 确定因果关系方向
            alpha = 0.05
            if p_value_lf < alpha and p_value_fl >= alpha:
                causality_direction = 'leader_causes_follower'
            elif p_value_fl < alpha and p_value_lf >= alpha:
                causality_direction = 'follower_causes_leader'
            elif p_value_lf < alpha and p_value_fl < alpha:
                causality_direction = 'bidirectional'
            else:
                causality_direction = 'no_causality'

            return {
                'causality_direction': causality_direction,
                'leader_to_follower': {
                    'p_value': p_value_lf,
                    'significant': p_value_lf < alpha,
                    'optimal_lag': best_lag_lf
                },
                'follower_to_leader': {
                    'p_value': p_value_fl,
                    'significant': p_value_fl < alpha,
                    'optimal_lag': best_lag_fl
                }
            }

        except Exception as e:
            return {'error': f'Granger causality test failed: {str(e)}'}

    def cointegration_test(self, leader_series, follower_series):
        """
        协整检验 - 检验长期均衡关系
        """
        try:
            data = pd.DataFrame({
                'leader': leader_series,
                'follower': follower_series
            }).dropna()

            if len(data) < 50:
                return {'error': 'Insufficient data for cointegration test'}

            # Engle-Granger协整检验
            coint_stat, p_value, critical_values = coint(data['leader'], data['follower'])

            return {
                'cointegrated': p_value < 0.05,
                'test_statistic': coint_stat,
                'p_value': p_value,
                'critical_values': {
                    '1%': critical_values[0],
                    '5%': critical_values[1],
                    '10%': critical_values[2]
                }
            }

        except Exception as e:
            return {'error': f'Cointegration test failed: {str(e)}'}

    def fit_var_model(self, leader_series, follower_series, max_lags=10):
        """
        拟合向量自回归(VAR)模型
        """
        try:
            data = pd.DataFrame({
                'leader': leader_series,
                'follower': follower_series
            }).dropna()

            if len(data) < max_lags * 5:
                return {'error': 'Insufficient data for VAR model'}

            # 拟合VAR模型
            model = VAR(data)

            # 选择最优滞后期
            lag_order_results = model.select_order(max_lags)
            optimal_lag = lag_order_results.aic

            # 拟合模型
            self.var_model = model.fit(optimal_lag)

            # 模型诊断
            residuals = self.var_model.resid

            # Ljung-Box检验（检验残差自相关）
            ljung_box_results = {}
            for col in residuals.columns:
                lb_stat, lb_pvalue = acorr_ljungbox(residuals[col], lags=10, return_df=False)
                ljung_box_results[col] = {
                    'statistic': lb_stat[-1],
                    'p_value': lb_pvalue[-1],
                    'no_autocorr': lb_pvalue[-1] > 0.05
                }

            return {
                'optimal_lag': optimal_lag,
                'aic': self.var_model.aic,
                'bic': self.var_model.bic,
                'model_summary': str(self.var_model.summary()),
                'ljung_box_test': ljung_box_results,
                'fitted': True
            }

        except Exception as e:
            return {'error': f'VAR model fitting failed: {str(e)}'}

    def dynamic_correlation_analysis(self, leader_series, follower_series,
                                   window_sizes=[60, 300, 900]):
        """
        动态相关性分析 - 多时间尺度滑动窗口
        """
        results = {}

        for window in window_sizes:
            if len(leader_series) < window * 2:
                continue

            correlations = []
            timestamps = []

            for i in range(window, len(leader_series)):
                leader_window = leader_series.iloc[i-window:i]
                follower_window = follower_series.iloc[i-window:i]

                # 计算相关系数
                corr = leader_window.corr(follower_window)
                if not pd.isna(corr):
                    correlations.append(corr)
                    timestamps.append(leader_series.index[i])

            if correlations:
                corr_series = pd.Series(correlations, index=timestamps)

                results[f'{window}s_window'] = {
                    'correlations': corr_series,
                    'mean_correlation': np.mean(correlations),
                    'std_correlation': np.std(correlations),
                    'min_correlation': np.min(correlations),
                    'max_correlation': np.max(correlations),
                    'correlation_stability': 1 - (np.std(correlations) / (np.mean(np.abs(correlations)) + 1e-8))
                }

        return results

class ProfitabilityAnalyzer:
    """
    盈利性评估系统 - 基于专家资料的全面盈利性分析

    功能：
    1. 夏普比率计算
    2. 最大回撤分析
    3. 胜率和盈亏比
    4. 交易费用和滑点成本考虑（支持maker/taker差异）
    5. 风险调整后收益指标
    6. 订单簿深度分析
    """

    def __init__(self, maker_fee_rate=0.0001, taker_fee_rate=0.0004, slippage_rate=0.0002):
        """
        初始化盈利性分析器

        Args:
            maker_fee_rate: Maker费率 (默认0.01%)
            taker_fee_rate: Taker费率 (默认0.04%)
            slippage_rate: 滑点率 (默认0.02%)
        """
        self.maker_fee_rate = maker_fee_rate
        self.taker_fee_rate = taker_fee_rate
        self.slippage_rate = slippage_rate
        self.maker_total_cost = maker_fee_rate + slippage_rate * 0.5  # Maker滑点更小
        self.taker_total_cost = taker_fee_rate + slippage_rate

    def calculate_returns_with_costs(self, raw_returns, position_changes=None,
                                   order_types=None, market_depth_scores=None):
        """
        计算考虑交易成本的净收益（支持maker/taker差异）

        Args:
            raw_returns: 原始收益率序列
            position_changes: 仓位变化序列（用于计算交易次数）
            order_types: 订单类型序列 ('maker' 或 'taker')
            market_depth_scores: 市场深度评分（0-1，用于评估maker成交概率）
        """
        if position_changes is None:
            # 假设每次都有交易，默认使用taker
            if order_types is None:
                trading_costs = pd.Series([self.taker_total_cost] * len(raw_returns),
                                        index=raw_returns.index)
            else:
                # 根据订单类型计算成本
                costs = []
                for order_type in order_types:
                    if order_type == 'maker':
                        costs.append(self.maker_total_cost)
                    else:
                        costs.append(self.taker_total_cost)
                trading_costs = pd.Series(costs, index=raw_returns.index)
        else:
            # 根据仓位变化和订单类型计算交易成本
            trades = (position_changes != position_changes.shift(1)).astype(int)

            if order_types is None:
                # 默认使用taker费率
                trading_costs = trades * self.taker_total_cost
            else:
                # 根据订单类型选择费率
                costs = []
                for i, (trade, order_type) in enumerate(zip(trades, order_types)):
                    if trade > 0:  # 有交易发生
                        if order_type == 'maker':
                            # 考虑maker成交概率
                            maker_prob = market_depth_scores[i] if market_depth_scores is not None else 0.8
                            expected_cost = (maker_prob * self.maker_total_cost +
                                           (1 - maker_prob) * self.taker_total_cost)
                            costs.append(expected_cost)
                        else:
                            costs.append(self.taker_total_cost)
                    else:
                        costs.append(0.0)
                trading_costs = pd.Series(costs, index=raw_returns.index)

        # 净收益 = 原始收益 - 交易成本
        net_returns = raw_returns - trading_costs

        return net_returns, trading_costs

    def analyze_market_depth(self, price_data, volume_data, window=60):
        """
        分析市场深度，评估maker订单成交概率

        Args:
            price_data: 价格数据
            volume_data: 成交量数据
            window: 分析窗口（秒）

        Returns:
            depth_scores: 深度评分（0-1，越高表示深度越好）
        """
        depth_scores = []

        for i in range(len(price_data)):
            if i < window:
                # 数据不足，使用默认评分
                depth_scores.append(0.6)
                continue

            # 获取窗口数据
            window_prices = price_data.iloc[i-window:i]
            window_volumes = volume_data.iloc[i-window:i] if volume_data is not None else None

            # 计算价格波动性
            price_volatility = window_prices.pct_change().std()

            # 计算成交量稳定性
            if window_volumes is not None:
                volume_stability = 1.0 / (1.0 + window_volumes.std() / window_volumes.mean())
            else:
                volume_stability = 0.5

            # 计算价格趋势强度
            price_trend = abs(window_prices.iloc[-1] - window_prices.iloc[0]) / window_prices.iloc[0]

            # 综合评分：低波动性 + 高成交量稳定性 + 低趋势强度 = 高maker成交概率
            volatility_score = max(0, 1.0 - price_volatility * 1000)  # 标准化波动性
            trend_score = max(0, 1.0 - price_trend * 100)  # 标准化趋势强度

            depth_score = (volatility_score * 0.4 + volume_stability * 0.3 + trend_score * 0.3)
            depth_score = max(0.1, min(0.95, depth_score))  # 限制在合理范围内

            depth_scores.append(depth_score)

        return pd.Series(depth_scores, index=price_data.index)

    def optimize_order_strategy(self, entry_signals, price_data, volume_data=None,
                              target_return_threshold=0.001):
        """
        优化订单策略：在合适的条件下使用maker订单

        Args:
            entry_signals: 入场信号时间序列
            price_data: 价格数据
            volume_data: 成交量数据
            target_return_threshold: 目标收益阈值

        Returns:
            order_strategies: 每个信号对应的订单策略 ('maker' 或 'taker')
        """
        # 分析市场深度
        depth_scores = self.analyze_market_depth(price_data, volume_data)

        order_strategies = []

        for signal_time in entry_signals.index:
            if signal_time not in depth_scores.index:
                order_strategies.append('taker')
                continue

            depth_score = depth_scores.loc[signal_time]

            # 决策逻辑：
            # 1. 深度评分高（>0.7）且预期收益大于阈值：使用maker
            # 2. 其他情况：使用taker确保成交
            expected_return = abs(entry_signals.loc[signal_time])

            if depth_score > 0.7 and expected_return > target_return_threshold:
                order_strategies.append('maker')
            else:
                order_strategies.append('taker')

        return pd.Series(order_strategies, index=entry_signals.index)

    def calculate_sharpe_ratio(self, returns, risk_free_rate=0.0, periods_per_year=252*24*60):
        """
        计算夏普比率 (年化)

        Args:
            returns: 收益率序列
            risk_free_rate: 无风险利率 (年化)
            periods_per_year: 每年的周期数 (默认按分钟计算)
        """
        if len(returns) == 0 or returns.std() == 0:
            return 0.0

        # 年化收益率和波动率
        annual_return = returns.mean() * periods_per_year
        annual_volatility = returns.std() * np.sqrt(periods_per_year)

        # 夏普比率
        sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility

        return sharpe_ratio

    def calculate_maximum_drawdown(self, returns):
        """
        计算最大回撤

        Returns:
            dict: 包含最大回撤、回撤期间等信息
        """
        if len(returns) == 0:
            return {'max_drawdown': 0.0, 'drawdown_duration': 0, 'recovery_time': 0}

        # 计算累积收益
        cumulative_returns = (1 + returns).cumprod()

        # 计算历史最高点
        running_max = cumulative_returns.expanding().max()

        # 计算回撤
        drawdown = (cumulative_returns - running_max) / running_max

        # 最大回撤
        max_drawdown = drawdown.min()

        # 找到最大回撤的开始和结束时间
        max_dd_end = drawdown.idxmin()
        max_dd_start = cumulative_returns.loc[:max_dd_end].idxmax()

        # 计算回撤持续时间
        try:
            # 检查索引是否为时间戳类型
            if hasattr(max_dd_end, 'total_seconds') and hasattr(max_dd_start, 'total_seconds'):
                # 如果是时间戳，计算时间差
                drawdown_duration = (max_dd_end - max_dd_start).total_seconds() / 60  # 分钟
            else:
                # 如果不是时间戳，尝试计算时间差
                time_diff = max_dd_end - max_dd_start
                if hasattr(time_diff, 'total_seconds'):
                    drawdown_duration = time_diff.total_seconds() / 60  # 分钟
                else:
                    # 如果索引不是时间戳，使用数据点数量
                    drawdown_duration = abs(max_dd_end - max_dd_start)  # 数据点数量
        except (AttributeError, TypeError):
            # 如果索引不是时间戳，使用数据点数量
            drawdown_duration = abs(max_dd_end - max_dd_start)  # 数据点数量

        # 计算恢复时间
        recovery_point = cumulative_returns.loc[max_dd_end:][
            cumulative_returns.loc[max_dd_end:] >= cumulative_returns.loc[max_dd_start]
        ]

        if len(recovery_point) > 0:
            try:
                # 检查索引是否为时间戳类型
                recovery_idx = recovery_point.index[0]
                if hasattr(recovery_idx, 'total_seconds') and hasattr(max_dd_end, 'total_seconds'):
                    # 如果是时间戳，计算时间差
                    recovery_time = (recovery_idx - max_dd_end).total_seconds() / 60
                else:
                    # 尝试计算时间差
                    time_diff = recovery_idx - max_dd_end
                    if hasattr(time_diff, 'total_seconds'):
                        recovery_time = time_diff.total_seconds() / 60
                    else:
                        # 如果索引不是时间戳，使用数据点数量
                        recovery_time = abs(recovery_idx - max_dd_end)  # 数据点数量
            except (AttributeError, TypeError):
                recovery_time = abs(recovery_point.index[0] - max_dd_end)  # 数据点数量
        else:
            recovery_time = np.inf

        return {
            'max_drawdown': abs(max_drawdown),
            'max_drawdown_start': max_dd_start,
            'max_drawdown_end': max_dd_end,
            'drawdown_duration_minutes': drawdown_duration,
            'recovery_time_minutes': recovery_time,
            'drawdown_series': drawdown
        }

    def calculate_win_rate_and_profit_loss_ratio(self, returns):
        """
        计算胜率和盈亏比

        Returns:
            dict: 胜率、盈亏比、平均盈利、平均亏损等指标
        """
        if len(returns) == 0:
            return {
                'win_rate': 0.0,
                'profit_loss_ratio': 0.0,
                'avg_profit': 0.0,
                'avg_loss': 0.0,
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0
            }

        # 分离盈利和亏损交易
        winning_trades = returns[returns > 0]
        losing_trades = returns[returns < 0]

        # 计算基本统计
        total_trades = len(returns)
        num_winning = len(winning_trades)
        num_losing = len(losing_trades)

        # 胜率
        win_rate = num_winning / total_trades if total_trades > 0 else 0.0

        # 平均盈利和亏损
        avg_profit = winning_trades.mean() if len(winning_trades) > 0 else 0.0
        avg_loss = abs(losing_trades.mean()) if len(losing_trades) > 0 else 0.0

        # 盈亏比
        profit_loss_ratio = avg_profit / avg_loss if avg_loss > 0 else np.inf

        return {
            'win_rate': win_rate,
            'profit_loss_ratio': profit_loss_ratio,
            'avg_profit': avg_profit,
            'avg_loss': avg_loss,
            'total_trades': total_trades,
            'winning_trades': num_winning,
            'losing_trades': num_losing
        }

    def calculate_calmar_ratio(self, returns):
        """
        计算卡尔玛比率 (年化收益率 / 最大回撤)
        """
        if len(returns) == 0:
            return 0.0

        annual_return = returns.mean() * 252 * 24 * 60  # 年化收益率
        max_dd_info = self.calculate_maximum_drawdown(returns)
        max_drawdown = max_dd_info['max_drawdown']

        if max_drawdown == 0:
            return np.inf if annual_return > 0 else 0.0

        calmar_ratio = annual_return / max_drawdown
        return calmar_ratio

    def calculate_sortino_ratio(self, returns, target_return=0.0, periods_per_year=252*24*60):
        """
        计算索提诺比率 (只考虑下行风险)
        """
        if len(returns) == 0:
            return 0.0

        # 年化收益率
        annual_return = returns.mean() * periods_per_year

        # 下行偏差 (只考虑低于目标收益的波动)
        downside_returns = returns[returns < target_return]
        if len(downside_returns) == 0:
            return np.inf if annual_return > target_return else 0.0

        downside_deviation = downside_returns.std() * np.sqrt(periods_per_year)

        sortino_ratio = (annual_return - target_return) / downside_deviation
        return sortino_ratio

    def comprehensive_performance_analysis(self, returns, benchmark_returns=None):
        """
        综合性能分析 - 生成完整的盈利性报告
        """
        # 考虑交易成本的净收益
        net_returns, trading_costs = self.calculate_returns_with_costs(returns)

        # 基本统计
        total_return = (1 + net_returns).prod() - 1
        annual_return = net_returns.mean() * 252 * 24 * 60
        annual_volatility = net_returns.std() * np.sqrt(252 * 24 * 60)

        # 风险调整指标
        sharpe_ratio = self.calculate_sharpe_ratio(net_returns)
        calmar_ratio = self.calculate_calmar_ratio(net_returns)
        sortino_ratio = self.calculate_sortino_ratio(net_returns)

        # 回撤分析
        drawdown_info = self.calculate_maximum_drawdown(net_returns)

        # 胜率分析
        win_loss_info = self.calculate_win_rate_and_profit_loss_ratio(net_returns)

        # 成本分析
        total_trading_costs = trading_costs.sum()
        cost_impact = (returns.sum() - net_returns.sum()) / abs(returns.sum()) if returns.sum() != 0 else 0

        performance_report = {
            'returns_analysis': {
                'total_return': total_return,
                'annual_return': annual_return,
                'annual_volatility': annual_volatility,
                'total_trading_costs': total_trading_costs,
                'cost_impact_pct': cost_impact * 100
            },
            'risk_adjusted_metrics': {
                'sharpe_ratio': sharpe_ratio,
                'calmar_ratio': calmar_ratio,
                'sortino_ratio': sortino_ratio
            },
            'drawdown_analysis': drawdown_info,
            'win_loss_analysis': win_loss_info
        }

        # 如果有基准，计算相对指标
        if benchmark_returns is not None:
            excess_returns = net_returns - benchmark_returns
            information_ratio = excess_returns.mean() / excess_returns.std() if excess_returns.std() > 0 else 0
            performance_report['relative_performance'] = {
                'information_ratio': information_ratio,
                'excess_return': excess_returns.sum()
            }

        return performance_report

class RiskManager:
    """
    风险管理模块 - 基于专家资料的全面风险控制

    功能：
    1. 动态止损机制
    2. 仓位管理
    3. 关系稳定性监控
    4. 事件风险评估
    5. 实时风险监控
    """

    def __init__(self, max_position_size=0.02, max_daily_loss=0.05,
                 correlation_threshold=0.3, volatility_threshold=2.0):
        """
        初始化风险管理器

        Args:
            max_position_size: 最大单次仓位 (占总资金比例)
            max_daily_loss: 最大日损失限制
            correlation_threshold: 相关性失效阈值
            volatility_threshold: 波动率异常阈值 (倍数)
        """
        self.max_position_size = max_position_size
        self.max_daily_loss = max_daily_loss
        self.correlation_threshold = correlation_threshold
        self.volatility_threshold = volatility_threshold
        self.daily_pnl = 0.0
        self.position_history = []
        self.risk_alerts = []

    def calculate_dynamic_stop_loss(self, entry_price, market_volatility,
                                  correlation_strength, multiplier=2.0):
        """
        动态止损计算 - 基于市场波动率和相关性强度

        Args:
            entry_price: 入场价格
            market_volatility: 市场波动率
            correlation_strength: 相关性强度 (0-1)
            multiplier: 止损倍数
        """
        # 基础止损 = 价格 * 波动率 * 倍数
        base_stop_loss = entry_price * market_volatility * multiplier

        # 根据相关性强度调整
        # 相关性越强，止损可以设置得更紧
        correlation_adjustment = 1.0 - (correlation_strength * 0.5)

        dynamic_stop_loss = base_stop_loss * correlation_adjustment

        return dynamic_stop_loss

    def calculate_position_size(self, account_balance, expected_return,
                              volatility, correlation_strength):
        """
        动态仓位计算 - Kelly公式改进版

        Args:
            account_balance: 账户余额
            expected_return: 预期收益率
            volatility: 策略波动率
            correlation_strength: 相关性强度
        """
        if volatility <= 0 or expected_return <= 0:
            return 0.0

        # Kelly公式: f = (bp - q) / b
        # 其中 b = 赔率, p = 胜率, q = 败率
        # 简化为: f = expected_return / variance

        kelly_fraction = expected_return / (volatility ** 2)

        # 根据相关性强度调整
        # 相关性越强，可以适当增加仓位
        correlation_adjustment = 0.5 + (correlation_strength * 0.5)
        adjusted_kelly = kelly_fraction * correlation_adjustment

        # 限制最大仓位
        position_fraction = min(adjusted_kelly, self.max_position_size)
        position_fraction = max(position_fraction, 0.0)  # 不允许负仓位

        position_size = account_balance * position_fraction

        return position_size

    def monitor_relationship_stability(self, recent_correlations, window=20):
        """
        监控Leader-Lag关系稳定性

        Args:
            recent_correlations: 最近的相关性序列
            window: 监控窗口大小
        """
        if len(recent_correlations) < window:
            return {'stable': True, 'warning': 'Insufficient data'}

        recent_window = recent_correlations[-window:]

        # 计算相关性的稳定性指标
        mean_correlation = recent_window.mean()
        std_correlation = recent_window.std()
        min_correlation = recent_window.min()

        # 稳定性检查
        stability_checks = {
            'mean_above_threshold': abs(mean_correlation) > self.correlation_threshold,
            'low_volatility': std_correlation < 0.3,
            'no_sign_change': (recent_window > 0).all() or (recent_window < 0).all(),
            'min_correlation_acceptable': abs(min_correlation) > self.correlation_threshold * 0.5
        }

        stable = all(stability_checks.values())

        # 生成警告信息
        warnings = []
        if not stability_checks['mean_above_threshold']:
            warnings.append(f"Mean correlation {mean_correlation:.3f} below threshold")
        if not stability_checks['low_volatility']:
            warnings.append(f"High correlation volatility: {std_correlation:.3f}")
        if not stability_checks['no_sign_change']:
            warnings.append("Correlation sign changes detected")
        if not stability_checks['min_correlation_acceptable']:
            warnings.append(f"Minimum correlation {min_correlation:.3f} too low")

        return {
            'stable': stable,
            'mean_correlation': mean_correlation,
            'correlation_volatility': std_correlation,
            'warnings': warnings,
            'stability_score': sum(stability_checks.values()) / len(stability_checks)
        }

    def assess_event_risk(self, liquidation_event, market_state, recent_performance):
        """
        事件风险评估

        Args:
            liquidation_event: 清算事件信息
            market_state: 当前市场状态
            recent_performance: 最近策略表现
        """
        risk_factors = {}

        # 清算规模风险
        liquidation_size = liquidation_event.get('quantity', 0)
        if liquidation_size > 1000000:  # 大额清算
            risk_factors['large_liquidation'] = 'HIGH'
        elif liquidation_size > 100000:
            risk_factors['large_liquidation'] = 'MEDIUM'
        else:
            risk_factors['large_liquidation'] = 'LOW'

        # 市场状态风险
        if market_state in ['high_volatility', 'anomaly']:
            risk_factors['market_state'] = 'HIGH'
        elif market_state in ['low_liquidity']:
            risk_factors['market_state'] = 'MEDIUM'
        else:
            risk_factors['market_state'] = 'LOW'

        # 策略表现风险
        if recent_performance.get('win_rate', 0) < 0.3:
            risk_factors['strategy_performance'] = 'HIGH'
        elif recent_performance.get('win_rate', 0) < 0.5:
            risk_factors['strategy_performance'] = 'MEDIUM'
        else:
            risk_factors['strategy_performance'] = 'LOW'

        # 综合风险评级
        high_risks = sum(1 for risk in risk_factors.values() if risk == 'HIGH')
        medium_risks = sum(1 for risk in risk_factors.values() if risk == 'MEDIUM')

        if high_risks >= 2:
            overall_risk = 'HIGH'
            recommended_action = 'SKIP_TRADE'
        elif high_risks == 1 or medium_risks >= 2:
            overall_risk = 'MEDIUM'
            recommended_action = 'REDUCE_POSITION'
        else:
            overall_risk = 'LOW'
            recommended_action = 'NORMAL_TRADE'

        return {
            'risk_factors': risk_factors,
            'overall_risk': overall_risk,
            'recommended_action': recommended_action,
            'risk_score': high_risks * 3 + medium_risks * 1
        }

class EnhancedLeaderLagStrategy:
    """
    增强的Leader-Lag策略执行器 - 整合所有分析模块

    功能：
    1. 多时间框架分析
    2. 事件驱动策略执行
    3. 实时风险监控
    4. 动态参数调整
    5. 综合性能评估
    """

    def __init__(self, symbol, trading_fee_rate=0.0004, slippage_rate=0.0002):
        self.symbol = symbol
        self.event_detector = LiquidationEventDetector()
        self.market_analyzer = MarketStateAnalyzer()
        self.statistical_analyzer = AdvancedStatisticalAnalyzer()
        self.profitability_analyzer = ProfitabilityAnalyzer(trading_fee_rate, slippage_rate)
        self.risk_manager = RiskManager()

        # 策略状态
        self.active_positions = {}
        self.performance_history = []
        self.correlation_history = []
        self.market_state_history = []

    def analyze_liquidation_event_enhanced(self, liquidation_event, binance_data, bybit_data):
        """
        增强的清算事件分析 - 整合所有分析模块

        Args:
            liquidation_event: 清算事件信息
            binance_data: Binance价格数据
            bybit_data: Bybit价格数据
        """
        entry_time = liquidation_event['timestamp']

        # 1. 事件窗口数据提取
        pre_window = timedelta(seconds=60)
        post_window = timedelta(seconds=60)

        pre_start = entry_time - pre_window
        post_end = entry_time + post_window

        # 提取分析窗口数据
        analysis_window = (binance_data.index >= pre_start) & (binance_data.index <= post_end)
        binance_window = binance_data[analysis_window]
        bybit_window = bybit_data[analysis_window]

        if len(binance_window) < 30 or len(bybit_window) < 30:
            return None

        # 2. 市场状态分析
        market_states = self.event_detector.classify_market_state(
            binance_window,
            pd.Series(index=binance_window.index, data=1000)  # 模拟成交量
        )
        current_market_state = market_states.loc[entry_time] if entry_time in market_states.index else 'normal'

        # 3. 条件化Leader-Lag分析
        conditional_results = self.market_analyzer.conditional_leader_lag_analysis(
            binance_window, bybit_window, market_states
        )

        # 4. 高级统计分析
        granger_results = self.statistical_analyzer.granger_causality_test(
            binance_window, bybit_window
        )

        cointegration_results = self.statistical_analyzer.cointegration_test(
            binance_window, bybit_window
        )

        dynamic_corr_results = self.statistical_analyzer.dynamic_correlation_analysis(
            binance_window, bybit_window
        )

        # 5. 传统相关性分析
        pre_entry_data = binance_window[binance_window.index < entry_time]
        post_entry_data = binance_window[binance_window.index >= entry_time]

        if len(pre_entry_data) < 10:
            return None

        lags, correlations = calculate_cross_correlation_efficient(
            pre_entry_data.values,
            bybit_window[bybit_window.index < entry_time].values
        )

        if len(correlations) == 0:
            return None

        # 找到最强相关性
        max_corr_idx = np.argmax(np.abs(correlations))
        max_correlation = correlations[max_corr_idx]
        optimal_lag = lags[max_corr_idx]

        # 6. 风险评估
        recent_performance = self.get_recent_performance()
        risk_assessment = self.risk_manager.assess_event_risk(
            liquidation_event, current_market_state, recent_performance
        )

        # 7. 信号生成和强度评估
        signal_strength = self.calculate_signal_strength(
            max_correlation, optimal_lag, conditional_results,
            granger_results, cointegration_results
        )

        # 8. 盈利性预测
        if len(post_entry_data) >= 10:
            # 计算实际收益（用于回测）
            entry_price_binance = binance_window.loc[entry_time] if entry_time in binance_window.index else binance_window.iloc[0]
            entry_price_bybit = bybit_window.loc[entry_time] if entry_time in bybit_window.index else bybit_window.iloc[0]

            # 10秒和30秒后的价格
            exit_10s = entry_time + timedelta(seconds=10)
            exit_30s = entry_time + timedelta(seconds=30)

            exit_price_10s_binance = binance_window[binance_window.index >= exit_10s].iloc[0] if len(binance_window[binance_window.index >= exit_10s]) > 0 else entry_price_binance
            exit_price_10s_bybit = bybit_window[bybit_window.index >= exit_10s].iloc[0] if len(bybit_window[bybit_window.index >= exit_10s]) > 0 else entry_price_bybit

            returns_10s = {
                'binance': (exit_price_10s_binance - entry_price_binance) / entry_price_binance,
                'bybit': (exit_price_10s_bybit - entry_price_bybit) / entry_price_bybit
            }
        else:
            returns_10s = {'binance': 0, 'bybit': 0}

        # 9. 综合分析结果
        analysis_result = {
            'entry_time': entry_time,
            'liquidation_event': liquidation_event,
            'market_state': current_market_state,
            'traditional_analysis': {
                'max_correlation': max_correlation,
                'optimal_lag': optimal_lag,
                'leader_exchange': 'binance' if optimal_lag < 0 else 'bybit' if optimal_lag > 0 else 'simultaneous'
            },
            'conditional_analysis': conditional_results,
            'granger_causality': granger_results,
            'cointegration': cointegration_results,
            'dynamic_correlation': dynamic_corr_results,
            'signal_strength': signal_strength,
            'risk_assessment': risk_assessment,
            'actual_returns_10s': returns_10s,
            'trading_recommendation': self.generate_trading_recommendation(
                signal_strength, risk_assessment, max_correlation, optimal_lag
            )
        }

        return analysis_result

    def calculate_signal_strength(self, correlation, lag, conditional_results,
                                granger_results, cointegration_results):
        """
        计算综合信号强度
        """
        strength_factors = []

        # 1. 相关性强度 (0-1)
        corr_strength = min(abs(correlation), 1.0)
        strength_factors.append(corr_strength)

        # 2. 格兰杰因果关系强度
        if 'error' not in granger_results:
            if granger_results['causality_direction'] in ['leader_causes_follower', 'follower_causes_leader']:
                granger_strength = 0.8
            elif granger_results['causality_direction'] == 'bidirectional':
                granger_strength = 0.6
            else:
                granger_strength = 0.2
        else:
            granger_strength = 0.3
        strength_factors.append(granger_strength)

        # 3. 协整关系强度
        if 'error' not in cointegration_results:
            coint_strength = 0.7 if cointegration_results['cointegrated'] else 0.3
        else:
            coint_strength = 0.3
        strength_factors.append(coint_strength)

        # 4. 条件化分析一致性
        if conditional_results:
            consistent_signals = sum(1 for state_result in conditional_results.values()
                                   if state_result.get('significance', {}).get('significant', False))
            consistency_strength = consistent_signals / len(conditional_results) if conditional_results else 0
        else:
            consistency_strength = 0.3
        strength_factors.append(consistency_strength)

        # 综合信号强度 (加权平均)
        weights = [0.4, 0.25, 0.2, 0.15]  # 相关性权重最高
        overall_strength = sum(w * s for w, s in zip(weights, strength_factors))

        return {
            'overall_strength': overall_strength,
            'correlation_strength': corr_strength,
            'granger_strength': granger_strength,
            'cointegration_strength': coint_strength,
            'consistency_strength': consistency_strength,
            'strength_level': self.classify_strength_level(overall_strength)
        }

    def classify_strength_level(self, strength):
        """分类信号强度等级"""
        if strength >= 0.8:
            return 'VERY_STRONG'
        elif strength >= 0.6:
            return 'STRONG'
        elif strength >= 0.4:
            return 'MEDIUM'
        elif strength >= 0.2:
            return 'WEAK'
        else:
            return 'VERY_WEAK'

    def generate_trading_recommendation(self, signal_strength, risk_assessment,
                                      correlation, lag):
        """
        生成交易建议
        """
        strength_level = signal_strength['strength_level']
        risk_level = risk_assessment['overall_risk']
        recommended_action = risk_assessment['recommended_action']

        # 基于信号强度和风险的决策矩阵
        if recommended_action == 'SKIP_TRADE':
            return {
                'action': 'NO_TRADE',
                'reason': 'High risk detected',
                'confidence': 0.0
            }

        if strength_level in ['VERY_STRONG', 'STRONG'] and risk_level == 'LOW':
            action = 'STRONG_BUY' if correlation > 0 else 'STRONG_SELL'
            confidence = 0.8
        elif strength_level == 'MEDIUM' and risk_level in ['LOW', 'MEDIUM']:
            action = 'BUY' if correlation > 0 else 'SELL'
            confidence = 0.6
        elif strength_level == 'WEAK' and risk_level == 'LOW':
            action = 'WEAK_BUY' if correlation > 0 else 'WEAK_SELL'
            confidence = 0.3
        else:
            action = 'NO_TRADE'
            confidence = 0.0

        # 确定交易方向和交易所
        if lag < -1:  # Binance领先
            leader_exchange = 'binance'
            follower_exchange = 'bybit'
        elif lag > 1:  # Bybit领先
            leader_exchange = 'bybit'
            follower_exchange = 'binance'
        else:
            leader_exchange = 'simultaneous'
            follower_exchange = 'simultaneous'

        return {
            'action': action,
            'confidence': confidence,
            'leader_exchange': leader_exchange,
            'follower_exchange': follower_exchange,
            'expected_lag': abs(lag),
            'signal_strength': signal_strength['overall_strength'],
            'risk_level': risk_level
        }

    def get_recent_performance(self, window=50):
        """获取最近的策略表现"""
        if len(self.performance_history) < window:
            return {'win_rate': 0.5, 'avg_return': 0.0, 'volatility': 0.02}

        recent_performance = self.performance_history[-window:]
        returns = [p['return'] for p in recent_performance]

        win_rate = sum(1 for r in returns if r > 0) / len(returns)
        avg_return = np.mean(returns)
        volatility = np.std(returns)

        return {
            'win_rate': win_rate,
            'avg_return': avg_return,
            'volatility': volatility
        }

    def comprehensive_performance_analysis(self, returns, benchmark_returns=None):
        """
        综合性能分析 - 生成完整的盈利性报告
        """
        # 考虑交易成本的净收益
        net_returns, trading_costs = self.calculate_returns_with_costs(returns)

        # 基本统计
        total_return = (1 + net_returns).prod() - 1
        annual_return = net_returns.mean() * 252 * 24 * 60
        annual_volatility = net_returns.std() * np.sqrt(252 * 24 * 60)

        # 风险调整指标
        sharpe_ratio = self.calculate_sharpe_ratio(net_returns)
        calmar_ratio = self.calculate_calmar_ratio(net_returns)
        sortino_ratio = self.calculate_sortino_ratio(net_returns)

        # 回撤分析
        drawdown_info = self.calculate_maximum_drawdown(net_returns)

        # 胜率分析
        win_loss_info = self.calculate_win_rate_and_profit_loss_ratio(net_returns)

        # 成本分析
        total_trading_costs = trading_costs.sum()
        cost_impact = (returns.sum() - net_returns.sum()) / abs(returns.sum()) if returns.sum() != 0 else 0

        performance_report = {
            'returns_analysis': {
                'total_return': total_return,
                'annual_return': annual_return,
                'annual_volatility': annual_volatility,
                'total_trading_costs': total_trading_costs,
                'cost_impact_pct': cost_impact * 100
            },
            'risk_adjusted_metrics': {
                'sharpe_ratio': sharpe_ratio,
                'calmar_ratio': calmar_ratio,
                'sortino_ratio': sortino_ratio
            },
            'drawdown_analysis': drawdown_info,
            'win_loss_analysis': win_loss_info
        }

        # 如果有基准，计算相对指标
        if benchmark_returns is not None:
            excess_returns = net_returns - benchmark_returns
            information_ratio = excess_returns.mean() / excess_returns.std() if excess_returns.std() > 0 else 0
            performance_report['relative_performance'] = {
                'information_ratio': information_ratio,
                'excess_return': excess_returns.sum()
            }

        return performance_report

class RiskManager:
    """
    风险管理模块 - 基于专家资料的全面风险控制

    功能：
    1. 动态止损机制
    2. 仓位管理
    3. 关系稳定性监控
    4. 事件风险评估
    5. 实时风险监控
    """

    def __init__(self, max_position_size=0.02, max_daily_loss=0.05,
                 correlation_threshold=0.3, volatility_threshold=2.0):
        """
        初始化风险管理器

        Args:
            max_position_size: 最大单次仓位 (占总资金比例)
            max_daily_loss: 最大日损失限制
            correlation_threshold: 相关性失效阈值
            volatility_threshold: 波动率异常阈值 (倍数)
        """
        self.max_position_size = max_position_size
        self.max_daily_loss = max_daily_loss
        self.correlation_threshold = correlation_threshold
        self.volatility_threshold = volatility_threshold
        self.daily_pnl = 0.0
        self.position_history = []
        self.risk_alerts = []

    def calculate_dynamic_stop_loss(self, entry_price, market_volatility,
                                  correlation_strength, multiplier=2.0):
        """
        动态止损计算 - 基于市场波动率和相关性强度

        Args:
            entry_price: 入场价格
            market_volatility: 市场波动率
            correlation_strength: 相关性强度 (0-1)
            multiplier: 止损倍数
        """
        # 基础止损 = 价格 * 波动率 * 倍数
        base_stop_loss = entry_price * market_volatility * multiplier

        # 根据相关性强度调整
        # 相关性越强，止损可以设置得更紧
        correlation_adjustment = 1.0 - (correlation_strength * 0.5)

        dynamic_stop_loss = base_stop_loss * correlation_adjustment

        return dynamic_stop_loss

    def calculate_position_size(self, account_balance, expected_return,
                              volatility, correlation_strength):
        """
        动态仓位计算 - Kelly公式改进版

        Args:
            account_balance: 账户余额
            expected_return: 预期收益率
            volatility: 策略波动率
            correlation_strength: 相关性强度
        """
        if volatility <= 0 or expected_return <= 0:
            return 0.0

        # Kelly公式: f = (bp - q) / b
        # 其中 b = 赔率, p = 胜率, q = 败率
        # 简化为: f = expected_return / variance

        kelly_fraction = expected_return / (volatility ** 2)

        # 根据相关性强度调整
        # 相关性越强，可以适当增加仓位
        correlation_adjustment = 0.5 + (correlation_strength * 0.5)
        adjusted_kelly = kelly_fraction * correlation_adjustment

        # 限制最大仓位
        position_fraction = min(adjusted_kelly, self.max_position_size)
        position_fraction = max(position_fraction, 0.0)  # 不允许负仓位

        position_size = account_balance * position_fraction

        return position_size

    def monitor_relationship_stability(self, recent_correlations, window=20):
        """
        监控Leader-Lag关系稳定性

        Args:
            recent_correlations: 最近的相关性序列
            window: 监控窗口大小
        """
        if len(recent_correlations) < window:
            return {'stable': True, 'warning': 'Insufficient data'}

        recent_window = recent_correlations[-window:]

        # 计算相关性的稳定性指标
        mean_correlation = recent_window.mean()
        std_correlation = recent_window.std()
        min_correlation = recent_window.min()

        # 稳定性检查
        stability_checks = {
            'mean_above_threshold': abs(mean_correlation) > self.correlation_threshold,
            'low_volatility': std_correlation < 0.3,
            'no_sign_change': (recent_window > 0).all() or (recent_window < 0).all(),
            'min_correlation_acceptable': abs(min_correlation) > self.correlation_threshold * 0.5
        }

        stable = all(stability_checks.values())

        # 生成警告信息
        warnings = []
        if not stability_checks['mean_above_threshold']:
            warnings.append(f"Mean correlation {mean_correlation:.3f} below threshold")
        if not stability_checks['low_volatility']:
            warnings.append(f"High correlation volatility: {std_correlation:.3f}")
        if not stability_checks['no_sign_change']:
            warnings.append("Correlation sign changes detected")
        if not stability_checks['min_correlation_acceptable']:
            warnings.append(f"Minimum correlation {min_correlation:.3f} too low")

        return {
            'stable': stable,
            'mean_correlation': mean_correlation,
            'correlation_volatility': std_correlation,
            'warnings': warnings,
            'stability_score': sum(stability_checks.values()) / len(stability_checks)
        }

    def assess_event_risk(self, liquidation_event, market_state, recent_performance):
        """
        事件风险评估

        Args:
            liquidation_event: 清算事件信息
            market_state: 当前市场状态
            recent_performance: 最近策略表现
        """
        risk_factors = {}

        # 清算规模风险
        liquidation_size = liquidation_event.get('quantity', 0)
        if liquidation_size > 1000000:  # 大额清算
            risk_factors['large_liquidation'] = 'HIGH'
        elif liquidation_size > 100000:
            risk_factors['large_liquidation'] = 'MEDIUM'
        else:
            risk_factors['large_liquidation'] = 'LOW'

        # 市场状态风险
        if market_state in ['high_volatility', 'anomaly']:
            risk_factors['market_state'] = 'HIGH'
        elif market_state in ['low_liquidity']:
            risk_factors['market_state'] = 'MEDIUM'
        else:
            risk_factors['market_state'] = 'LOW'

        # 策略表现风险
        if recent_performance.get('win_rate', 0) < 0.3:
            risk_factors['strategy_performance'] = 'HIGH'
        elif recent_performance.get('win_rate', 0) < 0.5:
            risk_factors['strategy_performance'] = 'MEDIUM'
        else:
            risk_factors['strategy_performance'] = 'LOW'

        # 综合风险评级
        high_risks = sum(1 for risk in risk_factors.values() if risk == 'HIGH')
        medium_risks = sum(1 for risk in risk_factors.values() if risk == 'MEDIUM')

        if high_risks >= 2:
            overall_risk = 'HIGH'
            recommended_action = 'SKIP_TRADE'
        elif high_risks == 1 or medium_risks >= 2:
            overall_risk = 'MEDIUM'
            recommended_action = 'REDUCE_POSITION'
        else:
            overall_risk = 'LOW'
            recommended_action = 'NORMAL_TRADE'

        return {
            'risk_factors': risk_factors,
            'overall_risk': overall_risk,
            'recommended_action': recommended_action,
            'risk_score': high_risks * 3 + medium_risks * 1
        }

def get_liquidation_timestamps(symbol):
    """Gets the timestamps of liquidation events for a given symbol with additional metadata."""
    files = glob.glob("data/*feather")
    liquidation_events = []

    for file in files:
        with open(file, 'rb') as source:
            reader = ipc.open_stream(source)
            table = reader.read_all()
            data = ArrowSerializer.deserialize(data_cls=BinanceFuturesLiquidationOrder, batch=table)
            for item in data:
                if item.instrument_id.value.replace('-PERP.BINANCE', '') == symbol:
                    liquidation_events.append({
                        'timestamp': pd.to_datetime(item.ts_event, unit='ns'),
                        'side': item.order_side.name,
                        'price': float(item.price.as_double()),
                        'quantity': float(item.original_quantity.as_double()),
                        'avg_price': float(item.avg_price.as_double())
                    })

    # Convert to DataFrame for easier analysis
    if liquidation_events:
        df = pd.DataFrame(liquidation_events)
        df = df.sort_values('timestamp').reset_index(drop=True)
        return df
    else:
        return pd.DataFrame()

def analyze_liquidation_time_range(liquidation_df, max_days=3):
    """Analyze liquidation data to determine optimal download time range (limited to few days)."""
    if liquidation_df.empty:
        return None, None

    # Get the time range of liquidation events
    min_time = liquidation_df['timestamp'].min()
    max_time = liquidation_df['timestamp'].max()

    # Limit to recent few days for fast analysis
    recent_date = max_time.date()
    start_date = recent_date - timedelta(days=max_days-1)
    end_date = recent_date

    print(f"Liquidation events span from {min_time.date()} to {max_time.date()}")
    print(f"For fast analysis, using only {max_days} days: {start_date} to {end_date}")

    return start_date, end_date

def check_trade_data_exists(symbol, exchange, date):
    """Check if trade data already exists for a given symbol, exchange, and date."""
    if exchange == 'binance':
        date_str = date.strftime("%Y-%m-%d")
        file_path = os.path.join("data", symbol, "trades", f"{symbol}-trades-{date_str}.parquet")
        return os.path.exists(file_path)
    elif exchange == 'bybit':
        date_str = date.strftime("%Y-%m-%d")
        file_path = os.path.join("data", symbol, "trades", f"{symbol}{date_str}.csv.gz")
        return os.path.exists(file_path)
    return False

def download_trade_data(symbol, liquidation_df):
    """Downloads trade data from Binance and Bybit for the optimal time range."""
    # Analyze liquidation data to get optimal time range
    start_date, end_date = analyze_liquidation_time_range(liquidation_df)

    if start_date is None or end_date is None:
        print("Could not determine time range from liquidation data")
        return

    # Create separate directories for each exchange
    binance_trades_dir = os.path.join("data", symbol, "trades")
    bybit_trades_dir = os.path.join("data", symbol, "trades")
    os.makedirs(binance_trades_dir, exist_ok=True)
    os.makedirs(bybit_trades_dir, exist_ok=True)

    binance_loader = BinanceTradesLoader("data")
    bybit_loader = BybitTradesLoader("data")

    # Generate date range
    current_date = start_date
    dates_to_download = []
    while current_date <= end_date:
        dates_to_download.append(current_date)
        current_date += timedelta(days=1)

    print(f"Checking and downloading trade data for {symbol} ({len(dates_to_download)} days)")

    # Download Binance data
    binance_missing_dates = []
    for date in dates_to_download:
        if not check_trade_data_exists(symbol, 'binance', date):
            binance_missing_dates.append(date)
        else:
            print(f"✓ Binance data already exists for {symbol} on {date}")

    if binance_missing_dates:
        print(f"Downloading Binance data for {len(binance_missing_dates)} missing dates")
        try:
            start_dt = datetime.combine(binance_missing_dates[0], datetime.min.time())
            end_dt = datetime.combine(binance_missing_dates[-1], datetime.min.time())
            binance_loader.load_data_for_range(symbol, start_dt, end_dt)
            print(f"✓ Binance trade data downloaded for {symbol}")
        except Exception as e:
            print(f"✗ Failed to download Binance data: {e}")
    else:
        print(f"✓ All Binance data already exists for {symbol}")

    # Download Bybit data
    bybit_missing_dates = []
    for date in dates_to_download:
        if not check_trade_data_exists(symbol, 'bybit', date):
            bybit_missing_dates.append(date)
        else:
            print(f"✓ Bybit data already exists for {symbol} on {date}")

    if bybit_missing_dates:
        print(f"Downloading Bybit data for {len(bybit_missing_dates)} missing dates")
        try:
            start_dt = datetime.combine(bybit_missing_dates[0], datetime.min.time())
            end_dt = datetime.combine(bybit_missing_dates[-1], datetime.min.time())
            bybit_loader.load_data_for_range(symbol, start_dt, end_dt)
            print(f"✓ Bybit trade data downloaded for {symbol}")
        except Exception as e:
            print(f"✗ Failed to download Bybit data: {e}")
    else:
        print(f"✓ All Bybit data already exists for {symbol}")

def load_trade_data(symbol, exchange):
    """Loads trade data for a given symbol and exchange with enhanced preprocessing."""
    try:
        if exchange == 'binance':
            # Look for Binance trade files
            files = glob.glob(f"data/{symbol}/trades/{symbol}-trades-*.parquet")
            if not files:
                print(f"No Binance trade files found for {symbol}")
                print(f"Looking in: data/{symbol}/trades/")
                return pd.DataFrame()

            print(f"Found {len(files)} Binance trade files for {symbol}")
            dfs = []
            for file in files:
                try:
                    df = pd.read_parquet(file)
                    # Check the actual column names in Binance data (only for first file)
                    if len(dfs) == 0:
                        print(f"Binance columns: {df.columns.tolist()}")
                    dfs.append(df)
                except Exception as e:
                    print(f"Error reading {file}: {e}")

            if not dfs:
                return pd.DataFrame()

            print(f"Concatenating {len(dfs)} Binance files...")
            df = pd.concat(dfs, ignore_index=True)
            print(f"Total Binance records: {len(df)}")

            # Handle different possible timestamp column names
            timestamp_col = None
            for col in ['timestamp', 'time', 'trade_time', 'T']:
                if col in df.columns:
                    timestamp_col = col
                    break

            if timestamp_col is None:
                print(f"No timestamp column found in Binance data. Columns: {df.columns.tolist()}")
                return pd.DataFrame()

            print(f"Converting timestamp column '{timestamp_col}' to datetime...")
            df['timestamp'] = pd.to_datetime(df[timestamp_col], unit='ms')
            df = df.set_index('timestamp').sort_index()

            # Handle different possible price column names
            price_col = None
            for col in ['price', 'p', 'Price']:
                if col in df.columns:
                    price_col = col
                    break

            if price_col is None:
                print(f"No price column found in Binance data. Columns: {df.columns.tolist()}")
                return pd.DataFrame()

            print(f"Converting price column '{price_col}' to numeric...")
            df['price'] = pd.to_numeric(df[price_col], errors='coerce')

            # Sample data if too large to avoid memory issues - be more aggressive for speed
            if len(df) > 100000:  # More than 100K records
                print(f"Sampling data from {len(df)} to 100K records for fast analysis")
                df = df.sample(n=100000, random_state=42).sort_index()

            print(f"Final Binance data shape: {df.shape}")
            return df

        elif exchange == 'bybit':
            # Look for Bybit trade files
            files = glob.glob(f"data/{symbol}/trades/{symbol}*.csv.gz")
            if not files:
                print(f"No Bybit trade files found for {symbol}")
                print(f"Looking in: data/{symbol}/trades/")
                return pd.DataFrame()

            print(f"Found {len(files)} Bybit trade files for {symbol}")
            dfs = []
            for file in files:
                try:
                    df = pd.read_csv(file, compression='gzip')
                    # Check the actual column names in Bybit data (only for first file)
                    if len(dfs) == 0:
                        print(f"Bybit columns: {df.columns.tolist()}")
                    dfs.append(df)
                except Exception as e:
                    print(f"Error reading {file}: {e}")

            if not dfs:
                return pd.DataFrame()

            print(f"Concatenating {len(dfs)} Bybit files...")
            df = pd.concat(dfs, ignore_index=True)
            print(f"Total Bybit records: {len(df)}")

            # Handle different possible timestamp column names for Bybit
            timestamp_col = None
            for col in ['timestamp', 'time', 'exec_time', 'trade_time']:
                if col in df.columns:
                    timestamp_col = col
                    break

            if timestamp_col is None:
                print(f"No timestamp column found in Bybit data. Columns: {df.columns.tolist()}")
                return pd.DataFrame()

            print(f"Converting Bybit timestamp column '{timestamp_col}' to datetime...")
            # Bybit timestamps are in Unix timestamp format (seconds with decimal)
            try:
                # First check if it's already in seconds (reasonable range)
                sample_ts = float(df[timestamp_col].iloc[0])
                print(f"Sample timestamp: {sample_ts}")

                if sample_ts > 1e12:  # Likely microseconds
                    print("Detected microsecond timestamps")
                    df['timestamp'] = pd.to_datetime(df[timestamp_col], unit='us')
                elif sample_ts > 1e9:  # Likely milliseconds or seconds
                    if sample_ts > 1e10:  # Likely milliseconds
                        print("Detected millisecond timestamps")
                        df['timestamp'] = pd.to_datetime(df[timestamp_col], unit='ms')
                    else:  # Likely seconds
                        print("Detected second timestamps")
                        df['timestamp'] = pd.to_datetime(df[timestamp_col], unit='s')
                else:
                    print("Timestamp format unclear, trying direct conversion")
                    df['timestamp'] = pd.to_datetime(df[timestamp_col])

            except Exception as e:
                print(f"Could not parse timestamp column {timestamp_col}: {e}")
                return pd.DataFrame()

            df = df.set_index('timestamp').sort_index()

            # Handle different possible price column names for Bybit
            price_col = None
            for col in ['price', 'exec_price', 'Price']:
                if col in df.columns:
                    price_col = col
                    break

            if price_col is None:
                print(f"No price column found in Bybit data. Columns: {df.columns.tolist()}")
                return pd.DataFrame()

            print(f"Converting Bybit price column '{price_col}' to numeric...")
            df['price'] = pd.to_numeric(df[price_col], errors='coerce')

            # Sample data if too large to avoid memory issues - be more aggressive for speed
            if len(df) > 100000:  # More than 100K records
                print(f"Sampling Bybit data from {len(df)} to 100K records for fast analysis")
                df = df.sample(n=100000, random_state=42).sort_index()

            print(f"Final Bybit data shape: {df.shape}")
            return df
        else:
            raise ValueError("Invalid exchange")

    except Exception as e:
        print(f"Error loading {exchange} data for {symbol}: {e}")
        return pd.DataFrame()

def determine_optimal_sampling_frequency(binance_data, bybit_data,
                                       base_freq='1S', high_vol_freq='100ms'):
    """
    根据市场波动性动态确定最优采样频率

    Args:
        binance_data: Binance价格数据
        bybit_data: Bybit价格数据
        base_freq: 基础采样频率
        high_vol_freq: 高波动期采样频率

    Returns:
        optimal_freq: 最优采样频率字符串
    """
    # 计算最近一段时间的价格波动性
    recent_window = min(1000, len(binance_data) // 4)  # 使用最近1/4的数据

    if recent_window < 100:
        return base_freq

    # 获取最近数据
    recent_binance = binance_data['price'].tail(recent_window)
    recent_bybit = bybit_data['price'].tail(recent_window)

    # 计算价格变化率的标准差（波动性指标）
    binance_volatility = recent_binance.pct_change().std()
    bybit_volatility = recent_bybit.pct_change().std()
    avg_volatility = (binance_volatility + bybit_volatility) / 2

    # 计算价格变化频率（连续变化的频率）
    binance_changes = (recent_binance.diff().abs() > 0).sum() / len(recent_binance)
    bybit_changes = (recent_bybit.diff().abs() > 0).sum() / len(recent_bybit)
    avg_change_freq = (binance_changes + bybit_changes) / 2

    # 决策逻辑：
    # 1. 高波动性（>0.001）且高变化频率（>0.5）：使用高频采样
    # 2. 中等波动性（>0.0005）：使用中频采样
    # 3. 低波动性：使用基础频率

    print(f"市场波动性分析: 平均波动率={avg_volatility:.6f}, 变化频率={avg_change_freq:.3f}")

    if avg_volatility > 0.001 and avg_change_freq > 0.5:
        print("检测到高波动市场，使用高频采样")
        return high_vol_freq
    elif avg_volatility > 0.0005:
        print("检测到中等波动市场，使用中频采样")
        return '500ms'
    else:
        print("检测到低波动市场，使用基础频率")
        return base_freq

def adaptive_window_analysis(price_data, volatility_threshold=0.001):
    """
    自适应窗口分析：根据市场状态调整分析窗口大小

    Args:
        price_data: 价格数据
        volatility_threshold: 波动性阈值

    Returns:
        optimal_windows: 最优分析窗口大小列表
    """
    optimal_windows = []

    # 滚动计算局部波动性
    rolling_volatility = price_data.pct_change().rolling(window=60).std()

    for vol in rolling_volatility:
        if pd.isna(vol):
            optimal_windows.append(30)  # 默认窗口
        elif vol > volatility_threshold:
            # 高波动期：使用较短窗口捕捉快速变化
            optimal_windows.append(15)
        elif vol > volatility_threshold * 0.5:
            # 中等波动期：使用中等窗口
            optimal_windows.append(30)
        else:
            # 低波动期：使用较长窗口提高信号稳定性
            optimal_windows.append(60)

    return optimal_windows

def calculate_cross_correlation_efficient(x, y, max_lag=30):
    """
    Efficient cross-correlation using numpy's correlate function.

    Mathematical approach:
    1. Normalize both series to zero mean, unit variance
    2. Use FFT-based correlation for O(n log n) complexity
    3. Extract relevant lags around zero

    Returns:
        lags: array of lag values (negative = x leads y, positive = y leads x)
        corrs: correlation coefficients for each lag
    """
    # Remove NaN values and ensure same length
    valid_idx = ~(np.isnan(x) | np.isnan(y))
    if valid_idx.sum() < 10:  # Need minimum data points
        return np.array([]), np.array([])

    x_clean = x[valid_idx]
    y_clean = y[valid_idx]

    # Normalize to zero mean, unit variance (z-score)
    x_norm = (x_clean - np.mean(x_clean)) / (np.std(x_clean) + 1e-8)
    y_norm = (y_clean - np.mean(y_clean)) / (np.std(y_clean) + 1e-8)

    # Calculate cross-correlation using numpy (FFT-based, much faster)
    correlation = np.correlate(x_norm, y_norm, mode='full')
    correlation = correlation / len(x_norm)  # Normalize by length

    # Extract lags around zero
    center = len(correlation) // 2
    start_idx = max(0, center - max_lag)
    end_idx = min(len(correlation), center + max_lag + 1)

    lags = np.arange(start_idx - center, end_idx - center)
    corrs = correlation[start_idx:end_idx]

    return lags, corrs

def analyze_price_movements(binance_prices, bybit_prices, window_minutes=10):
    """Analyze price movements and returns around liquidation events."""
    results = []

    # Calculate returns
    binance_returns = binance_prices.pct_change()
    bybit_returns = bybit_prices.pct_change()

    # Calculate rolling volatility
    binance_vol = binance_returns.rolling(window=60).std()  # 1-minute rolling volatility
    bybit_vol = bybit_returns.rolling(window=60).std()

    return {
        'binance_returns': binance_returns,
        'bybit_returns': bybit_returns,
        'binance_volatility': binance_vol,
        'bybit_volatility': bybit_vol
    }

def generate_trading_signals_efficient(lags, correlations, liquidation_time):
    """
    Generate trading signals based on lead-lag analysis with enhanced logic.

    Mathematical Interpretation:
    - Negative lag: Binance leads Bybit (Binance price moves first)
    - Positive lag: Bybit leads Binance (Bybit price moves first)
    - Zero lag: Simultaneous movement

    Trading Strategy:
    - If exchange A leads by T seconds with high correlation,
      we can potentially trade on exchange B after seeing movement on A
    """
    if len(correlations) == 0:
        return {
            'liquidation_time': liquidation_time,
            'leader_exchange': 'unknown',
            'follower_exchange': 'unknown',
            'lead_time_seconds': 0,
            'max_correlation': 0,
            'signal_strength': 'none'
        }

    # Find maximum absolute correlation
    max_corr_idx = np.argmax(np.abs(correlations))
    max_corr_lag = lags[max_corr_idx]
    max_corr_value = correlations[max_corr_idx]

    # Determine which exchange leads
    if max_corr_lag > 1:  # Bybit leads by more than 1 second
        leader = 'bybit'
        follower = 'binance'
        lead_time = max_corr_lag
    elif max_corr_lag < -1:  # Binance leads by more than 1 second
        leader = 'binance'
        follower = 'bybit'
        lead_time = abs(max_corr_lag)
    else:  # Simultaneous (within 1 second)
        leader = 'simultaneous'
        follower = 'simultaneous'
        lead_time = 0

    # Enhanced signal strength classification
    abs_corr = abs(max_corr_value)
    if abs_corr > 0.8:
        signal_strength = 'very_strong'
    elif abs_corr > 0.6:
        signal_strength = 'strong'
    elif abs_corr > 0.4:
        signal_strength = 'medium'
    elif abs_corr > 0.2:
        signal_strength = 'weak'
    else:
        signal_strength = 'very_weak'

    # Calculate additional metrics
    correlation_direction = 'positive' if max_corr_value > 0 else 'negative'

    return {
        'liquidation_time': liquidation_time,
        'leader_exchange': leader,
        'follower_exchange': follower,
        'lead_time_seconds': lead_time,
        'max_correlation': max_corr_value,
        'signal_strength': signal_strength,
        'correlation_direction': correlation_direction,
        'abs_correlation': abs_corr
    }

def evaluate_liquidation_entry_performance_enhanced(symbol, liquidation_df, sample_size=100):
    """
    增强的清算入场点性能评估 - 使用新的分析框架

    Args:
        symbol: 交易符号
        liquidation_df: 清算事件DataFrame
        sample_size: 分析样本大小
    """
    print(f"开始增强的清算入场点性能评估: {symbol}")

    # 初始化增强策略
    enhanced_strategy = EnhancedLeaderLagStrategy(symbol)

    # 筛选最近的清算事件
    recent_liquidations = liquidation_df[
        liquidation_df['timestamp'] >= (liquidation_df['timestamp'].max() - timedelta(days=3))
    ]

    if len(recent_liquidations) == 0:
        print("没有找到最近的清算事件")
        return pd.DataFrame()

    # 采样
    liquidation_sample = recent_liquidations.sample(
        n=min(sample_size, len(recent_liquidations)),
        random_state=42
    ).sort_values('timestamp')

    print(f"分析 {len(liquidation_sample)} 个清算事件")

    # 加载交易数据
    print("加载交易数据...")
    binance_data = load_trade_data(symbol, 'binance')
    bybit_data = load_trade_data(symbol, 'bybit')

    if binance_data.empty or bybit_data.empty:
        print("交易数据加载失败")
        return pd.DataFrame()

    # 动态采样频率 - 根据市场波动性调整采样频率
    print("分析市场波动性以确定最优采样频率...")
    optimal_freq = determine_optimal_sampling_frequency(binance_data, bybit_data)
    print(f"确定最优采样频率: {optimal_freq}")

    print(f"重采样交易数据到{optimal_freq}间隔...")
    print(f"Binance数据范围: {binance_data.index.min()} 到 {binance_data.index.max()}")
    binance_resampled = binance_data['price'].resample(optimal_freq).mean().ffill()
    print(f"Binance重采样形状: {binance_resampled.shape}")

    print(f"Bybit数据范围: {bybit_data.index.min()} 到 {bybit_data.index.max()}")
    bybit_resampled = bybit_data['price'].resample(optimal_freq).mean().ffill()
    print(f"Bybit重采样形状: {bybit_resampled.shape}")

    # 对齐两个时间序列
    print("对齐时间序列...")
    aligned_df = pd.concat([binance_resampled, bybit_resampled], axis=1, keys=['binance', 'bybit'])
    print(f"对齐后形状 (dropna前): {aligned_df.shape}")
    aligned_df = aligned_df.dropna()
    print(f"对齐后形状 (dropna后): {aligned_df.shape}")

    if aligned_df.empty:
        print("数据对齐失败 - 没有可用的对齐数据")
        return pd.DataFrame()

    # 增强分析
    enhanced_results = []

    for idx, liquidation in liquidation_sample.iterrows():
        try:
            # 使用增强分析框架
            result = enhanced_strategy.analyze_liquidation_event_enhanced(
                liquidation.to_dict(),
                aligned_df['binance'],
                aligned_df['bybit']
            )

            if result:
                enhanced_results.append(result)

            if len(enhanced_results) % 20 == 0:
                print(f"已处理 {len(enhanced_results)} 个事件...")

        except Exception as e:
            print(f"分析事件时出错 {liquidation['timestamp']}: {e}")
            continue

    if not enhanced_results:
        print("没有生成有效的分析结果")
        return pd.DataFrame()

    # 转换为DataFrame并生成报告
    results_df = pd.DataFrame(enhanced_results)

    # 生成增强的性能报告
    generate_enhanced_performance_summary(symbol, enhanced_results, enhanced_strategy)

    # 保存结果
    output_file = f"{symbol}_enhanced_liquidation_analysis.csv"

    # 展平嵌套字典以便保存
    flattened_results = []
    for result in enhanced_results:
        flat_result = flatten_analysis_result(result)
        flattened_results.append(flat_result)

    pd.DataFrame(flattened_results).to_csv(output_file, index=False)
    print(f"增强分析结果已保存到: {output_file}")

    return results_df

def flatten_analysis_result(result):
    """展平嵌套的分析结果字典"""
    flat = {}

    # 基本信息
    flat['entry_time'] = result['entry_time']
    flat['market_state'] = result['market_state']

    # 清算事件信息
    liq_event = result['liquidation_event']
    flat['liquidation_side'] = liq_event['side']
    flat['liquidation_price'] = liq_event['price']
    flat['liquidation_quantity'] = liq_event['quantity']

    # 传统分析
    trad = result['traditional_analysis']
    flat['max_correlation'] = trad['max_correlation']
    flat['optimal_lag'] = trad['optimal_lag']
    flat['leader_exchange'] = trad['leader_exchange']

    # 信号强度
    signal = result['signal_strength']
    flat['overall_signal_strength'] = signal['overall_strength']
    flat['strength_level'] = signal['strength_level']

    # 风险评估
    risk = result['risk_assessment']
    flat['overall_risk'] = risk['overall_risk']
    flat['risk_score'] = risk['risk_score']

    # 交易建议
    rec = result['trading_recommendation']
    flat['recommended_action'] = rec['action']
    flat['confidence'] = rec['confidence']

    # 实际收益
    returns = result['actual_returns_10s']
    flat['actual_return_binance_10s'] = returns['binance'] * 100
    flat['actual_return_bybit_10s'] = returns['bybit'] * 100

    # 格兰杰因果关系
    granger = result.get('granger_causality', {})
    if 'error' not in granger:
        flat['granger_causality_direction'] = granger.get('causality_direction', 'unknown')
    else:
        flat['granger_causality_direction'] = 'error'

    # 协整关系
    coint = result.get('cointegration', {})
    if 'error' not in coint:
        flat['cointegrated'] = coint.get('cointegrated', False)
        flat['cointegration_pvalue'] = coint.get('p_value', 1.0)
    else:
        flat['cointegrated'] = False
        flat['cointegration_pvalue'] = 1.0

    return flat

def generate_enhanced_performance_summary(symbol, results_list, strategy):
    """
    生成增强的性能分析报告

    Args:
        symbol: 交易符号
        results_list: 分析结果列表 (字典列表)
        strategy: 策略对象
    """
    print("\n" + "="*100)
    print(f"增强的清算事件Leader-Lag分析报告 - {symbol}")
    print("="*100)

    if len(results_list) == 0:
        print("没有可分析的数据")
        return

    total_events = len(results_list)
    print(f"总分析事件数: {total_events}")

    # 1. 市场状态分布
    print(f"\n📊 市场状态分布:")
    market_states = [r['market_state'] for r in results_list]
    state_counts = pd.Series(market_states).value_counts()
    for state, count in state_counts.items():
        print(f"  {state}: {count} ({count/total_events*100:.1f}%)")

    # 2. 信号强度分析
    print(f"\n🎯 信号强度分析:")
    strength_levels = [r['signal_strength']['strength_level'] for r in results_list]
    strength_counts = pd.Series(strength_levels).value_counts()
    for level, count in strength_counts.items():
        print(f"  {level}: {count} ({count/total_events*100:.1f}%)")

    avg_strength = np.mean([r['signal_strength']['overall_strength'] for r in results_list])
    print(f"  平均信号强度: {avg_strength:.3f}")

    # 3. 风险评估分析
    print(f"\n⚠️ 风险评估分析:")
    risk_levels = [r['risk_assessment']['overall_risk'] for r in results_list]
    risk_counts = pd.Series(risk_levels).value_counts()
    for level, count in risk_counts.items():
        print(f"  {level} 风险: {count} ({count/total_events*100:.1f}%)")

    # 4. 交易建议分析
    print(f"\n💡 交易建议分析:")
    recommendations = [r['trading_recommendation']['action'] for r in results_list]
    rec_counts = pd.Series(recommendations).value_counts()
    for action, count in rec_counts.items():
        print(f"  {action}: {count} ({count/total_events*100:.1f}%)")

    # 5. 格兰杰因果关系分析
    print(f"\n🔗 格兰杰因果关系分析:")
    causality_results = []
    for r in results_list:
        granger = r.get('granger_causality', {})
        if 'error' not in granger:
            causality_results.append(granger['causality_direction'])
        else:
            causality_results.append('error')

    if causality_results:
        causality_counts = pd.Series(causality_results).value_counts()
        for direction, count in causality_counts.items():
            print(f"  {direction}: {count} ({count/len(causality_results)*100:.1f}%)")

    # 6. 协整关系分析
    print(f"\n🔄 协整关系分析:")
    cointegrated_count = 0
    total_coint_tests = 0

    for r in results_list:
        coint = r.get('cointegration', {})
        if 'error' not in coint:
            total_coint_tests += 1
            if coint.get('cointegrated', False):
                cointegrated_count += 1

    if total_coint_tests > 0:
        coint_rate = cointegrated_count / total_coint_tests
        print(f"  协整关系检出率: {coint_rate:.1%} ({cointegrated_count}/{total_coint_tests})")

    # 7. 盈利性分析 (考虑交易成本)
    print(f"\n💰 盈利性分析 (考虑交易成本):")

    # 提取实际收益
    binance_returns = [r['actual_returns_10s']['binance'] for r in results_list]
    bybit_returns = [r['actual_returns_10s']['bybit'] for r in results_list]

    # 使用盈利性分析器
    profitability_analyzer = strategy.profitability_analyzer

    # Binance策略分析
    binance_performance = profitability_analyzer.comprehensive_performance_analysis(
        pd.Series(binance_returns)
    )

    # Bybit策略分析
    bybit_performance = profitability_analyzer.comprehensive_performance_analysis(
        pd.Series(bybit_returns)
    )

    print(f"  📈 Binance策略 (考虑成本后):")
    print(f"    年化收益率: {binance_performance['returns_analysis']['annual_return']*100:.2f}%")
    print(f"    夏普比率: {binance_performance['risk_adjusted_metrics']['sharpe_ratio']:.3f}")
    print(f"    最大回撤: {binance_performance['drawdown_analysis']['max_drawdown']*100:.2f}%")
    print(f"    胜率: {binance_performance['win_loss_analysis']['win_rate']*100:.1f}%")
    print(f"    成本影响: {binance_performance['returns_analysis']['cost_impact_pct']:.2f}%")

    print(f"  📈 Bybit策略 (考虑成本后):")
    print(f"    年化收益率: {bybit_performance['returns_analysis']['annual_return']*100:.2f}%")
    print(f"    夏普比率: {bybit_performance['risk_adjusted_metrics']['sharpe_ratio']:.3f}")
    print(f"    最大回撤: {bybit_performance['drawdown_analysis']['max_drawdown']*100:.2f}%")
    print(f"    胜率: {bybit_performance['win_loss_analysis']['win_rate']*100:.1f}%")
    print(f"    成本影响: {bybit_performance['returns_analysis']['cost_impact_pct']:.2f}%")

    # 8. 智能策略分析 (基于信号强度选择)
    print(f"\n🧠 智能策略分析:")
    smart_returns = []

    for r in results_list:
        recommendation = r['trading_recommendation']
        if recommendation['action'] not in ['NO_TRADE']:
            # 根据推荐选择交易所
            if recommendation['follower_exchange'] == 'binance':
                smart_returns.append(r['actual_returns_10s']['binance'])
            elif recommendation['follower_exchange'] == 'bybit':
                smart_returns.append(r['actual_returns_10s']['bybit'])
            else:
                # 选择表现更好的交易所
                binance_ret = r['actual_returns_10s']['binance']
                bybit_ret = r['actual_returns_10s']['bybit']
                smart_returns.append(max(binance_ret, bybit_ret, key=abs))

    if smart_returns:
        smart_performance = profitability_analyzer.comprehensive_performance_analysis(
            pd.Series(smart_returns)
        )

        print(f"  📊 智能选择策略 (考虑成本后):")
        print(f"    交易机会: {len(smart_returns)}/{total_events} ({len(smart_returns)/total_events*100:.1f}%)")
        print(f"    年化收益率: {smart_performance['returns_analysis']['annual_return']*100:.2f}%")
        print(f"    夏普比率: {smart_performance['risk_adjusted_metrics']['sharpe_ratio']:.3f}")
        print(f"    最大回撤: {smart_performance['drawdown_analysis']['max_drawdown']*100:.2f}%")
        print(f"    胜率: {smart_performance['win_loss_analysis']['win_rate']*100:.1f}%")
        print(f"    盈亏比: {smart_performance['win_loss_analysis']['profit_loss_ratio']:.2f}")

    # 9. 策略建议
    print(f"\n💡 策略建议:")

    # 找出最佳表现的条件
    high_strength_events = [r for r in results_list if r['signal_strength']['strength_level'] in ['VERY_STRONG', 'STRONG']]
    low_risk_events = [r for r in results_list if r['risk_assessment']['overall_risk'] == 'LOW']

    if high_strength_events:
        high_strength_returns = [r['actual_returns_10s']['bybit'] for r in high_strength_events]
        avg_high_strength_return = np.mean(high_strength_returns) * 100
        print(f"  🎯 高信号强度事件平均收益: {avg_high_strength_return:.3f}% ({len(high_strength_events)}个事件)")

    if low_risk_events:
        low_risk_returns = [r['actual_returns_10s']['bybit'] for r in low_risk_events]
        avg_low_risk_return = np.mean(low_risk_returns) * 100
        print(f"  🛡️ 低风险事件平均收益: {avg_low_risk_return:.3f}% ({len(low_risk_events)}个事件)")

    # 最优条件组合
    optimal_events = [r for r in results_list
                     if r['signal_strength']['strength_level'] in ['VERY_STRONG', 'STRONG']
                     and r['risk_assessment']['overall_risk'] == 'LOW']

    if optimal_events:
        optimal_returns = [r['actual_returns_10s']['bybit'] for r in optimal_events]
        avg_optimal_return = np.mean(optimal_returns) * 100
        print(f"  ⭐ 最优条件组合平均收益: {avg_optimal_return:.3f}% ({len(optimal_events)}个事件)")
        print(f"     建议: 仅在高信号强度且低风险时交易")

    print("="*100)

def evaluate_liquidation_entry_performance(symbol, liquidation_df, sample_size=100):
    """
    Evaluate trading performance using liquidation timestamps as entry points.

    Focus: Determine if liquidation events provide profitable entry signals
    based on lead-lag patterns between exchanges.

    Args:
        symbol: Trading symbol
        liquidation_df: DataFrame of liquidation events
        sample_size: Maximum number of liquidations to analyze
    """
    print(f"Evaluating liquidation entry performance for {symbol}")

    # Filter to recent liquidations only and sample
    recent_liquidations = liquidation_df[liquidation_df['timestamp'] >= (liquidation_df['timestamp'].max() - timedelta(days=3))]

    if len(recent_liquidations) > sample_size:
        print(f"Sampling {sample_size} recent liquidations from {len(recent_liquidations)} events in last 3 days")
        liquidation_sample = recent_liquidations.sample(n=sample_size, random_state=42).sort_values('timestamp')
    else:
        liquidation_sample = recent_liquidations.copy()
        print(f"Using all {len(liquidation_sample)} recent liquidations from last 3 days")

    print(f"Analyzing {len(liquidation_sample)} liquidation entry points")

    # Load trade data
    binance_df = load_trade_data(symbol, 'binance')
    bybit_df = load_trade_data(symbol, 'bybit')

    if binance_df.empty or bybit_df.empty:
        print(f"Insufficient trade data for {symbol}")
        return None

    # Resample to 1-second frequency
    print("Resampling trade data to 1-second intervals...")
    print(f"Binance data range: {binance_df.index.min()} to {binance_df.index.max()}")
    binance_resampled = binance_df['price'].resample('1S').mean().ffill()
    print(f"Binance resampled shape: {binance_resampled.shape}")

    print(f"Bybit data range: {bybit_df.index.min()} to {bybit_df.index.max()}")
    bybit_resampled = bybit_df['price'].resample('1S').mean().ffill()
    print(f"Bybit resampled shape: {bybit_resampled.shape}")

    # Align the two time series
    print("Aligning time series...")
    aligned_df = pd.concat([binance_resampled, bybit_resampled], axis=1, keys=['binance', 'bybit'])
    print(f"Aligned shape before dropna: {aligned_df.shape}")
    aligned_df = aligned_df.dropna()
    print(f"Aligned shape after dropna: {aligned_df.shape}")

    if aligned_df.empty:
        print(f"No aligned data available for {symbol}")
        return None

    print(f"Aligned data shape: {aligned_df.shape}")
    print(f"Data range: {aligned_df.index.min()} to {aligned_df.index.max()}")

    # Evaluate each liquidation as a potential entry point
    entry_evaluations = []

    for idx, liquidation in liquidation_sample.iterrows():
        entry_time = liquidation['timestamp']

        # Analyze lead-lag pattern around this entry point
        evaluation = evaluate_single_entry_point(entry_time, liquidation, aligned_df)
        if evaluation:
            entry_evaluations.append(evaluation)

        if len(entry_evaluations) % 100 == 0:
            print(f"Processed {len(entry_evaluations)} entry points...")

    # Convert to DataFrame and analyze results
    if entry_evaluations:
        results_df = pd.DataFrame(entry_evaluations)

        # Generate performance summary
        generate_entry_performance_summary(symbol, results_df)

        # Save results
        results_df.to_csv(f"{symbol}_liquidation_entry_performance.csv", index=False)
        print(f"Entry performance results saved to {symbol}_liquidation_entry_performance.csv")

        return results_df
    else:
        print("No valid entry point evaluations generated")
        return pd.DataFrame()

def evaluate_single_entry_point(entry_time, liquidation, aligned_df):
    """
    Evaluate a single liquidation timestamp as a trading entry point.

    Analysis:
    1. Look at price movement 30 seconds before entry (to detect lead-lag)
    2. Measure price movement 30 seconds after entry (to evaluate performance)
    3. Determine which exchange leads and by how much
    4. Calculate potential profit if trading on the lagging exchange
    """

    # Define time windows
    pre_entry_start = entry_time - timedelta(seconds=30)
    post_entry_end = entry_time + timedelta(seconds=30)

    # Extract data around entry point
    window_df = aligned_df[
        (aligned_df.index >= pre_entry_start) &
        (aligned_df.index <= post_entry_end)
    ]

    if len(window_df) < 20:  # Need minimum data
        return None

    try:
        # Split into pre and post entry periods
        pre_entry_df = window_df[window_df.index < entry_time]
        post_entry_df = window_df[window_df.index >= entry_time]

        if len(pre_entry_df) < 10 or len(post_entry_df) < 10:
            return None

        # Calculate lead-lag in pre-entry period (last 30 seconds)
        lags, corrs = calculate_cross_correlation_efficient(
            pre_entry_df['binance'].values,
            pre_entry_df['bybit'].values,
            max_lag=10
        )

        if len(lags) == 0:
            return None

        # Find the leading exchange
        max_corr_idx = np.argmax(np.abs(corrs))
        max_corr_lag = lags[max_corr_idx]
        max_corr_value = corrs[max_corr_idx]

        # Determine leader/follower
        if max_corr_lag > 1:
            leader = 'bybit'
            follower = 'binance'
            lead_time = max_corr_lag
        elif max_corr_lag < -1:
            leader = 'binance'
            follower = 'bybit'
            lead_time = abs(max_corr_lag)
        else:
            leader = 'simultaneous'
            follower = 'simultaneous'
            lead_time = 0

        # Calculate entry and exit prices
        entry_price_binance = window_df.loc[window_df.index >= entry_time, 'binance'].iloc[0]
        entry_price_bybit = window_df.loc[window_df.index >= entry_time, 'bybit'].iloc[0]

        # Calculate prices 10 and 30 seconds after entry
        exit_10s = entry_time + timedelta(seconds=10)
        exit_30s = entry_time + timedelta(seconds=30)

        exit_price_10s_binance = window_df.loc[window_df.index >= exit_10s, 'binance'].iloc[0] if len(window_df[window_df.index >= exit_10s]) > 0 else entry_price_binance
        exit_price_10s_bybit = window_df.loc[window_df.index >= exit_10s, 'bybit'].iloc[0] if len(window_df[window_df.index >= exit_10s]) > 0 else entry_price_bybit

        exit_price_30s_binance = window_df.loc[window_df.index >= exit_30s, 'binance'].iloc[0] if len(window_df[window_df.index >= exit_30s]) > 0 else entry_price_binance
        exit_price_30s_bybit = window_df.loc[window_df.index >= exit_30s, 'bybit'].iloc[0] if len(window_df[window_df.index >= exit_30s]) > 0 else entry_price_bybit

        # Calculate returns for different strategies
        # Strategy 1: Trade on Binance based on liquidation signal
        binance_return_10s = (exit_price_10s_binance - entry_price_binance) / entry_price_binance * 100
        binance_return_30s = (exit_price_30s_binance - entry_price_binance) / entry_price_binance * 100

        # Strategy 2: Trade on Bybit based on liquidation signal
        bybit_return_10s = (exit_price_10s_bybit - entry_price_bybit) / entry_price_bybit * 100
        bybit_return_30s = (exit_price_30s_bybit - entry_price_bybit) / entry_price_bybit * 100

        # Strategy 3: Trade on follower exchange if there's a clear leader
        follower_return_10s = 0
        follower_return_30s = 0

        if leader != 'simultaneous':
            if follower == 'binance':
                follower_return_10s = binance_return_10s
                follower_return_30s = binance_return_30s
            else:
                follower_return_10s = bybit_return_10s
                follower_return_30s = bybit_return_30s

        return {
            'entry_time': entry_time,
            'liquidation_side': liquidation['side'],
            'liquidation_price': liquidation['price'],
            'liquidation_quantity': liquidation['quantity'],
            'leader_exchange': leader,
            'follower_exchange': follower,
            'lead_time_seconds': lead_time,
            'max_correlation': max_corr_value,
            'entry_price_binance': entry_price_binance,
            'entry_price_bybit': entry_price_bybit,
            'binance_return_10s': binance_return_10s,
            'binance_return_30s': binance_return_30s,
            'bybit_return_10s': bybit_return_10s,
            'bybit_return_30s': bybit_return_30s,
            'follower_return_10s': follower_return_10s,
            'follower_return_30s': follower_return_30s,
            'abs_correlation': abs(max_corr_value)
        }

    except Exception as e:
        print(f"Error evaluating entry point at {entry_time}: {e}")
        return None

def create_liquidation_analysis_plot(symbol, window_df, lags, corrs, signals, liquidation_time):
    """Create comprehensive visualization for liquidation analysis."""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

    # Plot 1: Price comparison
    ax1.plot(window_df.index, window_df['binance'], label='Binance', alpha=0.8)
    ax1.plot(window_df.index, window_df['bybit'], label='Bybit', alpha=0.8)
    ax1.axvline(liquidation_time, color='red', linestyle='--', label='Liquidation')
    ax1.set_title(f'{symbol} Price Comparison Around Liquidation')
    ax1.set_ylabel('Price')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Plot 2: Cross-correlation
    ax2.plot(lags, corrs)
    max_corr_idx = np.argmax(np.abs(corrs))
    ax2.scatter(lags[max_corr_idx], corrs[max_corr_idx], color='red', s=100, zorder=5)
    ax2.set_title(f'Cross-correlation (Max: {corrs[max_corr_idx]:.3f} at lag {lags[max_corr_idx]}s)')
    ax2.set_xlabel('Lag (seconds)')
    ax2.set_ylabel('Correlation')
    ax2.grid(True, alpha=0.3)

    # Plot 3: Price difference
    price_diff = window_df['binance'] - window_df['bybit']
    ax3.plot(window_df.index, price_diff)
    ax3.axhline(0, color='black', linestyle='-', alpha=0.3)
    ax3.axvline(liquidation_time, color='red', linestyle='--')
    ax3.set_title('Price Difference (Binance - Bybit)')
    ax3.set_ylabel('Price Difference')
    ax3.grid(True, alpha=0.3)

    # Plot 4: Returns comparison
    binance_returns = window_df['binance'].pct_change() * 10000  # in basis points
    bybit_returns = window_df['bybit'].pct_change() * 10000
    ax4.scatter(binance_returns, bybit_returns, alpha=0.6)
    ax4.plot([binance_returns.min(), binance_returns.max()],
             [binance_returns.min(), binance_returns.max()], 'r--', alpha=0.5)
    ax4.set_title('Returns Correlation')
    ax4.set_xlabel('Binance Returns (bps)')
    ax4.set_ylabel('Bybit Returns (bps)')
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    filename = f"liquidation_analysis_{symbol}_{liquidation_time.strftime('%Y%m%d_%H%M%S')}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"Analysis plot saved: {filename}")

def generate_entry_performance_summary(symbol, results_df):
    """Generate summary of liquidation entry point performance."""
    print("\n" + "="*80)
    print(f"LIQUIDATION ENTRY POINT PERFORMANCE ANALYSIS FOR {symbol}")
    print("="*80)

    total_entries = len(results_df)
    if total_entries == 0:
        print("No valid entry points to analyze.")
        return

    print(f"Total liquidation entry points analyzed: {total_entries}")

    # Lead-lag pattern analysis
    leader_counts = results_df['leader_exchange'].value_counts()
    print(f"\nLead-Lag Patterns at Entry Points:")
    for leader, count in leader_counts.items():
        print(f"  {leader.title()}: {count} ({count/total_entries*100:.1f}%)")

    # Performance by exchange
    print(f"\nPerformance by Exchange (10-second holding period):")
    binance_10s_mean = results_df['binance_return_10s'].mean()
    bybit_10s_mean = results_df['bybit_return_10s'].mean()
    print(f"  Binance average return: {binance_10s_mean:.4f}%")
    print(f"  Bybit average return: {bybit_10s_mean:.4f}%")

    print(f"\nPerformance by Exchange (30-second holding period):")
    binance_30s_mean = results_df['binance_return_30s'].mean()
    bybit_30s_mean = results_df['bybit_return_30s'].mean()
    print(f"  Binance average return: {binance_30s_mean:.4f}%")
    print(f"  Bybit average return: {bybit_30s_mean:.4f}%")

    # Strategy performance: Trade on follower exchange
    follower_trades = results_df[results_df['leader_exchange'] != 'simultaneous']
    if len(follower_trades) > 0:
        follower_10s_mean = follower_trades['follower_return_10s'].mean()
        follower_30s_mean = follower_trades['follower_return_30s'].mean()
        print(f"\nFollower Exchange Strategy:")
        print(f"  Opportunities with clear leader: {len(follower_trades)} ({len(follower_trades)/total_entries*100:.1f}%)")
        print(f"  Average return (10s): {follower_10s_mean:.4f}%")
        print(f"  Average return (30s): {follower_30s_mean:.4f}%")

        # Win rate analysis
        follower_wins_10s = len(follower_trades[follower_trades['follower_return_10s'] > 0])
        follower_wins_30s = len(follower_trades[follower_trades['follower_return_30s'] > 0])
        print(f"  Win rate (10s): {follower_wins_10s/len(follower_trades)*100:.1f}%")
        print(f"  Win rate (30s): {follower_wins_30s/len(follower_trades)*100:.1f}%")

    # High correlation opportunities
    high_corr_trades = results_df[results_df['abs_correlation'] > 0.5]
    if len(high_corr_trades) > 0:
        print(f"\nHigh Correlation Opportunities (>0.5):")
        print(f"  Count: {len(high_corr_trades)} ({len(high_corr_trades)/total_entries*100:.1f}%)")
        print(f"  Binance return (10s): {high_corr_trades['binance_return_10s'].mean():.4f}%")
        print(f"  Bybit return (10s): {high_corr_trades['bybit_return_10s'].mean():.4f}%")

        if len(high_corr_trades[high_corr_trades['leader_exchange'] != 'simultaneous']) > 0:
            high_corr_follower = high_corr_trades[high_corr_trades['leader_exchange'] != 'simultaneous']
            print(f"  Follower strategy return (10s): {high_corr_follower['follower_return_10s'].mean():.4f}%")

    # Liquidation side analysis
    print(f"\nPerformance by Liquidation Side:")
    for side in results_df['liquidation_side'].unique():
        side_data = results_df[results_df['liquidation_side'] == side]
        print(f"  {side} liquidations: {len(side_data)} events")
        print(f"    Binance return (10s): {side_data['binance_return_10s'].mean():.4f}%")
        print(f"    Bybit return (10s): {side_data['bybit_return_10s'].mean():.4f}%")

    # Best performing strategy summary
    print(f"\nStrategy Comparison (10-second returns):")
    strategies = {
        'Always Binance': results_df['binance_return_10s'].mean(),
        'Always Bybit': results_df['bybit_return_10s'].mean(),
    }

    if len(follower_trades) > 0:
        strategies['Follower Strategy'] = follower_trades['follower_return_10s'].mean()

    best_strategy = max(strategies.items(), key=lambda x: x[1])
    print(f"  Best strategy: {best_strategy[0]} ({best_strategy[1]:.4f}% average return)")

    for strategy, return_val in strategies.items():
        print(f"  {strategy}: {return_val:.4f}%")

    print("="*80)

def generate_analysis_summary(symbol, results_df):
    """Generate comprehensive analysis summary with enhanced metrics."""
    print("\n" + "="*70)
    print(f"ENHANCED LEAD-LAG ANALYSIS SUMMARY FOR {symbol}")
    print("="*70)

    # Basic statistics
    total_events = len(results_df)
    if total_events == 0:
        print("No valid analysis results to summarize.")
        return

    # Signal strength distribution
    signal_counts = results_df['signal_strength'].value_counts()
    print(f"Total liquidation events analyzed: {total_events}")
    print(f"\nSignal Strength Distribution:")
    for strength, count in signal_counts.items():
        print(f"  {strength.replace('_', ' ').title()}: {count} ({count/total_events*100:.1f}%)")

    # Lead-lag statistics
    leader_counts = results_df['leader_exchange'].value_counts()
    print(f"\nLead-Lag Patterns:")
    for leader, count in leader_counts.items():
        print(f"  {leader.title()}: {count} ({count/total_events*100:.1f}%)")

    # Average lead times by exchange
    for exchange in ['binance', 'bybit']:
        exchange_leads = results_df[results_df['leader_exchange'] == exchange]
        if len(exchange_leads) > 0:
            avg_lead = exchange_leads['lead_time_seconds'].mean()
            max_lead = exchange_leads['lead_time_seconds'].max()
            print(f"  {exchange.title()} average lead time: {avg_lead:.1f}s (max: {max_lead:.1f}s)")

    # Correlation statistics
    print(f"\nCorrelation Analysis:")
    print(f"  Average correlation: {results_df['max_correlation'].mean():.3f}")
    print(f"  Median correlation: {results_df['max_correlation'].median():.3f}")
    print(f"  Max correlation: {results_df['max_correlation'].max():.3f}")
    print(f"  Min correlation: {results_df['max_correlation'].min():.3f}")

    # Correlation direction analysis
    if 'correlation_direction' in results_df.columns:
        direction_counts = results_df['correlation_direction'].value_counts()
        print(f"  Positive correlations: {direction_counts.get('positive', 0)} ({direction_counts.get('positive', 0)/total_events*100:.1f}%)")
        print(f"  Negative correlations: {direction_counts.get('negative', 0)} ({direction_counts.get('negative', 0)/total_events*100:.1f}%)")

    # Trading opportunity analysis
    strong_signals = results_df[results_df['signal_strength'].isin(['strong', 'very_strong'])]
    profitable_opportunities = results_df[
        (results_df['signal_strength'].isin(['strong', 'very_strong', 'medium'])) &
        (results_df['lead_time_seconds'] >= 2)  # At least 2 seconds lead time
    ]

    print(f"\nTrading Opportunity Analysis:")
    print(f"  Strong signals (correlation > 0.6): {len(strong_signals)} ({len(strong_signals)/total_events*100:.1f}%)")
    print(f"  Actionable opportunities (2+ sec lead, medium+ signal): {len(profitable_opportunities)} ({len(profitable_opportunities)/total_events*100:.1f}%)")

    if len(profitable_opportunities) > 0:
        avg_lead_time = profitable_opportunities['lead_time_seconds'].mean()
        avg_correlation = profitable_opportunities['max_correlation'].mean()
        print(f"  Average lead time for opportunities: {avg_lead_time:.1f} seconds")
        print(f"  Average correlation for opportunities: {avg_correlation:.3f}")

        # Liquidation side analysis
        if 'liquidation_side' in profitable_opportunities.columns:
            side_counts = profitable_opportunities['liquidation_side'].value_counts()
            print(f"  Opportunities by liquidation side:")
            for side, count in side_counts.items():
                print(f"    {side}: {count} ({count/len(profitable_opportunities)*100:.1f}%)")

    # Mathematical insights
    print(f"\nMathematical Insights:")
    lead_time_std = results_df['lead_time_seconds'].std()
    correlation_std = results_df['max_correlation'].std()
    print(f"  Lead time variability (std): {lead_time_std:.2f} seconds")
    print(f"  Correlation variability (std): {correlation_std:.3f}")

    # Efficiency metrics
    non_simultaneous = results_df[results_df['leader_exchange'] != 'simultaneous']
    if len(non_simultaneous) > 0:
        efficiency_ratio = len(non_simultaneous) / total_events
        print(f"  Market efficiency ratio: {efficiency_ratio:.3f} (lower = more efficient)")

    print("="*70)

def main(symbol, use_enhanced=True):
    """
    主函数 - 分析清算事件及其Leader-Lag关系

    Args:
        symbol: 交易符号
        use_enhanced: 是否使用增强分析框架
    """
    print(f"开始 {symbol} 的清算事件分析")

    # 获取清算事件
    if os.path.exists(f"{symbol}_liquid_data.csv"):
        liquidation_df = pd.read_csv(f"{symbol}_liquid_data.csv")
        liquidation_df['timestamp'] = pd.to_datetime(liquidation_df['timestamp'])
    else:
        liquidation_df = get_liquidation_timestamps(symbol)
        liquidation_df.to_csv(f"{symbol}_liquid_data.csv", index=False)

    if liquidation_df.empty:
        print(f"未找到 {symbol} 的清算事件")
        return

    print(f"找到 {len(liquidation_df)} 个清算事件")

    # 基于清算时间范围下载交易数据
    download_trade_data(symbol, liquidation_df)

    if use_enhanced:
        # 使用增强分析框架
        print("🚀 使用增强分析框架...")
        results = evaluate_liquidation_entry_performance_enhanced(symbol, liquidation_df)

        if results is not None and not results.empty:
            print(f"✅ 增强分析完成")

            # 同时运行传统分析进行对比
            print("\n📊 运行传统分析进行对比...")
            traditional_results = evaluate_liquidation_entry_performance(symbol, liquidation_df)

            if traditional_results is not None and not traditional_results.empty:
                print("✅ 传统分析也已完成，可以对比两种方法的结果")
        else:
            print("⚠️ 增强分析无法完成，尝试传统分析...")
            results = evaluate_liquidation_entry_performance(symbol, liquidation_df)
    else:
        # 使用传统分析
        print("📈 使用传统分析框架...")
        results = evaluate_liquidation_entry_performance(symbol, liquidation_df)

    if results is not None and not results.empty:
        print(f"🎉 {symbol} 的清算入场点评估完成")
        print(f"💾 结果已保存")
    else:
        print(f"❌ 由于数据不足，无法完成评估")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='执行增强的Leader-Lag分析')
    parser.add_argument('symbol', type=str, help='要分析的交易符号 (例如: FUNUSDT)')
    parser.add_argument('--enhanced', action='store_true', default=True,
                       help='使用增强分析框架 (默认: True)')
    parser.add_argument('--traditional', action='store_true',
                       help='仅使用传统分析框架')

    args = parser.parse_args()

    # 如果指定了traditional，则不使用enhanced
    use_enhanced = not args.traditional if args.traditional else args.enhanced

    main(args.symbol, use_enhanced=use_enhanced)
