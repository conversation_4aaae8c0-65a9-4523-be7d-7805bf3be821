import pandas as pd
import requests
import json
import time
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Set, Tuple
import logging
from pathlib import Path

# Import the existing data loaders
from binance_data_loader import TradesLoader as BinanceTradesLoader
from bybit_data_loader import TradesLoader as BybitTradesLoader

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cross_exchange_downloader.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CrossExchangeDataDownloader:
    """
    Downloads trade data for symbols that exist on both Binance and Bybit futures exchanges.
    """
    
    def __init__(self, output_dir: str = "data"):
        """
        Initialize the cross-exchange data downloader.
        
        Args:
            output_dir (str): Base directory for storing downloaded data
        """
        self.output_dir = output_dir
        self.binance_trades_loader = BinanceTradesLoader(output_dir)
        self.bybit_trades_loader = BybitTradesLoader(output_dir)
        
    def load_liquidation_symbols(self, csv_path: str = "top_liquidations_symbols.csv") -> List[str]:
        """
        Load symbols from the liquidation analysis CSV file.
        
        Args:
            csv_path (str): Path to the CSV file containing liquidation symbols
            
        Returns:
            List[str]: List of base symbols (without exchange suffix)
        """
        try:
            df = pd.read_csv(csv_path)
            symbols = []
            
            for instrument_id in df['instrument_id']:
                # Convert from "FUNUSDT-PERP.BINANCE" to "FUNUSDT"
                base_symbol = instrument_id.replace('-PERP.BINANCE', '')
                symbols.append(base_symbol)
                
            logger.info(f"Loaded {len(symbols)} symbols from {csv_path}")
            return symbols
            
        except Exception as e:
            logger.error(f"Error loading symbols from {csv_path}: {e}")
            return []
    
    def get_binance_futures_symbols(self) -> Set[str]:
        """
        Get all available symbols from Binance Futures exchange.
        
        Returns:
            Set[str]: Set of available Binance futures symbols
        """
        try:
            url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            symbols = {symbol_info['symbol'] for symbol_info in data['symbols'] 
                      if symbol_info['status'] == 'TRADING'}
            
            logger.info(f"Found {len(symbols)} active symbols on Binance Futures")
            return symbols
            
        except Exception as e:
            logger.error(f"Error fetching Binance symbols: {e}")
            return set()
    
    def get_bybit_futures_symbols(self) -> Set[str]:
        """
        Get all available symbols from Bybit Futures exchange.
        
        Returns:
            Set[str]: Set of available Bybit futures symbols
        """
        try:
            url = "https://api.bybit.com/v5/market/instruments-info"
            params = {"category": "linear"}  # Linear futures
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            symbols = {item['symbol'] for item in data['result']['list'] 
                      if item['status'] == 'Trading'}
            
            logger.info(f"Found {len(symbols)} active symbols on Bybit Futures")
            return symbols
            
        except Exception as e:
            logger.error(f"Error fetching Bybit symbols: {e}")
            return set()
    
    def load_common_symbols_from_bybit_filtered_csv(self, csv_path: str = "top_liquidations_symbols_bybit.csv") -> List[str]:
        """
        Load symbols from the Bybit-filtered liquidation analysis CSV file.
        
        Args:
            csv_path (str): Path to the CSV file containing Bybit-filtered liquidation symbols
            
        Returns:
            List[str]: List of base symbols (without exchange suffix)
        """
        try:
            df = pd.read_csv(csv_path)
            symbols = []
            
            for instrument_id in df['instrument_id']:
                # Convert from "FUNUSDT-PERP.BINANCE" to "FUNUSDT"
                base_symbol = instrument_id.replace('-PERP.BINANCE', '')
                symbols.append(base_symbol)
                
            logger.info(f"Loaded {len(symbols)} common symbols from {csv_path}")
            return symbols
            
        except Exception as e:
            logger.error(f"Error loading symbols from {csv_path}: {e}")
            return []
    
    def download_symbol_data(self, symbol: str, start_date: datetime, end_date: datetime) -> Dict[str, bool]:
        """
        Download trade data for a symbol from both exchanges.
        
        Args:
            symbol (str): Symbol to download data for
            start_date (datetime): Start date for data download
            end_date (datetime): End date for data download
            
        Returns:
            Dict[str, bool]: Success status for each exchange
        """
        results = {"binance": False, "bybit": False}
        
        try:
            # Download from Binance
            logger.info(f"Downloading Binance data for {symbol}")
            self.binance_trades_loader.load_data_for_range(symbol, start_date, end_date)
            results["binance"] = True
            logger.info(f"✓ Binance data download completed for {symbol}")
            
        except Exception as e:
            logger.error(f"✗ Binance data download failed for {symbol}: {e}")
        
        try:
            # Download from Bybit
            logger.info(f"Downloading Bybit data for {symbol}")
            self.bybit_trades_loader.load_data_for_range(symbol, start_date, end_date)
            results["bybit"] = True
            logger.info(f"✓ Bybit data download completed for {symbol}")
            
        except Exception as e:
            logger.error(f"✗ Bybit data download failed for {symbol}: {e}")
        
        return results
    
    def download_all_data(self, symbols: List[str], days: int = 10, max_workers: int = 3) -> Dict[str, Dict[str, bool]]:
        """
        Download trade data for all symbols from both exchanges.
        
        Args:
            symbols (List[str]): List of symbols to download data for
            days (int): Number of recent days to download
            max_workers (int): Maximum number of concurrent downloads
            
        Returns:
            Dict[str, Dict[str, bool]]: Download results for each symbol and exchange
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        logger.info(f"Starting data download for {len(symbols)} symbols")
        logger.info(f"Date range: {start_date.date()} to {end_date.date()}")
        
        results = {}
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit download tasks
            future_to_symbol = {
                executor.submit(self.download_symbol_data, symbol, start_date, end_date): symbol
                for symbol in symbols
            }
            
            # Process completed tasks
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    result = future.result()
                    results[symbol] = result
                    logger.info(f"Completed downloads for {symbol}")
                except Exception as e:
                    logger.error(f"Error downloading data for {symbol}: {e}")
                    results[symbol] = {"binance": False, "bybit": False}
        
        return results
    
    def save_results_summary(self, common_symbols: List[str], download_results: Dict[str, Dict[str, bool]]) -> None:
        """
        Save a summary of the analysis and download results.
        
        Args:
            common_symbols (List[str]): List of symbols available on both exchanges
            download_results (Dict[str, Dict[str, bool]]): Download results for each symbol
        """
        summary = {
            "analysis_timestamp": datetime.now().isoformat(),
            "total_liquidation_symbols": len(self.load_liquidation_symbols()),
            "common_symbols_count": len(common_symbols),
            "common_symbols": common_symbols,
            "download_results": download_results
        }
        
        # Save as JSON
        with open("cross_exchange_analysis_summary.json", "w") as f:
            json.dump(summary, f, indent=2)
        
        # Save common symbols as CSV for easy reference
        df = pd.DataFrame({"symbol": common_symbols})
        df.to_csv("common_symbols_both_exchanges.csv", index=False)
        
        logger.info("Results summary saved to cross_exchange_analysis_summary.json")
        logger.info("Common symbols saved to common_symbols_both_exchanges.csv")

def main(symbol: str):
    """Main function to run the cross-exchange data download process."""
    downloader = CrossExchangeDataDownloader()
    
    # Step 1: Download recent 10 days of trade data for the specified symbol
    download_results = downloader.download_all_data([symbol], days=10)
    
    # Step 2: Save results summary
    downloader.save_results_summary([symbol], download_results)
    
    # Print final summary
    successful_downloads = sum(1 for result in download_results.values() 
                             if result.get("binance", False) and result.get("bybit", False))
    
    logger.info("=" * 50)
    logger.info("FINAL SUMMARY")
    logger.info("=" * 50)
    logger.info(f"Symbols analyzed: {symbol}")
    logger.info(f"Successful downloads from both exchanges: {successful_downloads}")
    logger.info("=" * 50)

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='Download trade data for a specific symbol from Binance and Bybit.')
    parser.add_argument('symbol', type=str, help='The symbol to download data for (e.g., FUNUSDT).')
    args = parser.parse_args()
    main(args.symbol)
