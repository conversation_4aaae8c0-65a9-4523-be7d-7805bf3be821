
from decimal import Decimal
from typing import Any
import pyarrow as pa

from nautilus_trader.core.data import Data
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.objects import Price
from nautilus_trader.model.objects import Quantity
from nautilus_trader.model.functions import order_side_to_str
from nautilus_trader.model.functions import order_side_from_str
from nautilus_trader.model.functions import order_type_to_str
from nautilus_trader.model.functions import order_type_from_str
from nautilus_trader.model.functions import time_in_force_to_str
from nautilus_trader.model.functions import time_in_force_from_str
from nautilus_trader.model.enums import order_status_from_str
from nautilus_trader.model.enums import order_status_to_str
from nautilus_trader.model.enums import OrderSide
from nautilus_trader.model.enums import OrderType
from nautilus_trader.model.enums import TimeInForce
from nautilus_trader.model.enums import OrderStatus


class BinanceFuturesLiquidationOrder(Data):
    """
    Represents a Binance Futures liquidation order.

    Parameters
    ----------
    instrument_id : InstrumentId
        The instrument ID for the liquidation order.
    order_side : OrderSide
        The side of the liquidation order (BUY/SELL).
    order_type : OrderType 
        The type of the liquidation order (usually LIMIT).
    time_in_force : TimeInForce
        The time in force of the liquidation order (usually IOC).
    original_quantity : Quantity
        The original quantity of the liquidation order.
    price : Price
        The price of the liquidation order.
    avg_price : Price
        The average fill price of the liquidation order.
    order_status : OrderStatus
        The status of the liquidation order.
    last_filled_quantity : Quantity
        The last filled quantity.
    accumulated_filled_quantity : Quantity
        The accumulated filled quantity.
    ts_event : uint64_t
        The timestamp when the event occurred.
    ts_init : uint64_t
        The timestamp when the object was initialized.

    References
    ----------
    https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Liquidation-Order-Streams
    """

    def __init__(
        self,
        instrument_id: InstrumentId,
        order_side: OrderSide,
        order_type: OrderType,
        time_in_force: TimeInForce,
        original_quantity: Quantity,
        price: Price,
        avg_price: Price,
        order_status: OrderStatus,
        last_filled_quantity: Quantity,
        accumulated_filled_quantity: Quantity,
        ts_event: int,
        ts_init: int,
    ):
        self.instrument_id = instrument_id
        self.order_side = order_side
        self.order_type = order_type
        self.time_in_force = time_in_force
        self.original_quantity = original_quantity
        self.price = price
        self.avg_price = avg_price
        self.order_status = order_status
        self.last_filled_quantity = last_filled_quantity
        self.accumulated_filled_quantity = accumulated_filled_quantity
        self._ts_event = ts_event
        self._ts_init = ts_init

    def __repr__(self) -> str:
        order_status = order_status_to_str(self.order_status)
        return (
            f"{type(self).__name__}("
            f"instrument_id={self.instrument_id}, "
            f"order_side={order_side_to_str(self.order_side)}, "
            f"order_type={order_type_to_str(self.order_type)}, "
            f"time_in_force={time_in_force_to_str(self.time_in_force)}, "
            f"original_quantity={self.original_quantity}, "
            f"price={self.price}, "
            f"avg_price={self.avg_price}, "
            f"order_status={order_status}, "
            f"last_filled_quantity={self.last_filled_quantity}, "
            f"accumulated_filled_quantity={self.accumulated_filled_quantity}, "
            f"ts_event={self.ts_event}, "
            f"ts_init={self.ts_init})"
        )

    @property
    def ts_event(self) -> int:
        return self._ts_event

    @property
    def ts_init(self) -> int:
        return self._ts_init

    @staticmethod
    def from_dict(values: dict[str, Any]) -> "BinanceFuturesLiquidationOrder":
        """
        Return a Binance Futures liquidation order parsed from the given values.
        """
        return BinanceFuturesLiquidationOrder(
            instrument_id=InstrumentId.from_str(values["instrument_id"]),
            order_side=order_side_from_str(values["order_side"]),
            order_type=order_type_from_str(values["order_type"]),
            time_in_force=time_in_force_from_str(values["time_in_force"]),
            original_quantity=Quantity.from_str(values["original_quantity"]),
            price=Price.from_str(values["price"]),
            avg_price=Price.from_str(values["avg_price"]),
            order_status=order_status_from_str(values["order_status"]),
            last_filled_quantity=Quantity.from_str(values["last_filled_quantity"]),
            accumulated_filled_quantity=Quantity.from_str(values["accumulated_filled_quantity"]),
            ts_event=values["ts_event"],
            ts_init=values["ts_init"],
        )

    @staticmethod
    def to_dict(obj: "BinanceFuturesLiquidationOrder") -> dict[str, Any]:
        """
        Return a dictionary representation of this object.
        """
        return {
            "type": type(obj).__name__,
            "instrument_id": str(obj.instrument_id),
            "order_side": order_side_to_str(obj.order_side),
            "order_type": order_type_to_str(obj.order_type),
            "time_in_force": time_in_force_to_str(obj.time_in_force),
            "original_quantity": str(obj.original_quantity),
            "price": str(obj.price),
            "avg_price": str(obj.avg_price),
            "order_status": order_status_to_str(obj.order_status),
            "last_filled_quantity": str(obj.last_filled_quantity),
            "accumulated_filled_quantity": str(obj.accumulated_filled_quantity),
            "ts_event": obj.ts_event,
            "ts_init": obj.ts_init,
        }

    @classmethod
    def schema(cls):
        return pa.schema(
            {
                "instrument_id": pa.string(),
                "order_side": pa.dictionary(pa.int8(), pa.string()),
                "order_type": pa.dictionary(pa.int8(), pa.string()),
                "time_in_force": pa.dictionary(pa.int8(), pa.string()),
                "original_quantity": pa.string(),
                "price": pa.string(),
                "avg_price": pa.string(),
                "order_status": pa.dictionary(pa.int8(), pa.string()),
                "last_filled_quantity": pa.string(),
                "accumulated_filled_quantity": pa.string(),
                "ts_event": pa.uint64(),
                "ts_init": pa.uint64(),
            }
        )
    @staticmethod
    def to_catalog(obj: "BinanceFuturesLiquidationOrder"):
        return pa.RecordBatch.from_pylist([BinanceFuturesLiquidationOrder.to_dict(obj)], schema=BinanceFuturesLiquidationOrder.schema())

    @classmethod
    def from_catalog(cls, table: pa.Table):
        return [BinanceFuturesLiquidationOrder.from_dict(d) for d in table.to_pylist()] 
