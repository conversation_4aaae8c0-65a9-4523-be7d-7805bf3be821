import os
import pandas as pd
import requests
import json
from datetime import datetime, timedelta
import time
from concurrent.futures import ThreadPoolExecutor
from typing import Optional
from io import BytesIO
from zipfile import ZipFile


class BybitDataLoader:
    """Base class for loading data from ByBit"""
    
    def __init__(self, base_url: str, output_base_dir: str, data_type: str):
        """
        Initialize the ByBit data loader.
        
        Args:
            base_url (str): Base URL template for downloading data
            output_base_dir (str): Base directory for storing data
            data_type (str): Type of data being downloaded (e.g., 'trades', 'orderbook')
        """
        self.base_url = base_url
        self.output_base_dir = output_base_dir
        self.data_type = data_type

    def data_exists(self, symbol: str, date: datetime) -> bool:
        """Check if data already exists for the given symbol and date."""
        date_str = date.strftime("%Y-%m-%d")
        file_path = os.path.join(self.output_base_dir, symbol, self.data_type, f"{date_str}_{symbol}_ob500.data.zip")
        return os.path.exists(file_path)

    def download_data(self, symbol: str, date: datetime) -> bytes:
        """Download data from ByBit for the given symbol and date."""
        formatted_date = date.strftime("%Y-%m-%d")
        url = self.base_url.format(symbol=symbol, date=formatted_date)
        response = requests.get(url)
        response.raise_for_status()
        return response.content

    def save_data(self, content: bytes, symbol: str, date: datetime) -> None:
        """Save the downloaded data directly without any conversion."""
        date_str = date.strftime("%Y-%m-%d")
        output_path = os.path.join(self.output_base_dir, symbol, self.data_type)
        if not os.path.exists(output_path):
            os.makedirs(output_path)
            
        # Save with original filename format
        file_path = os.path.join(output_path, f"{date_str}_{symbol}_ob500.data.zip")
        with open(file_path, "wb") as f:
            f.write(content)
        print(f"Raw data saved to {file_path} for {symbol}")

    def load_data_for_date(self, symbol: str, date: datetime) -> None:
        """Load data for a specific symbol and date."""
        if self.data_exists(symbol, date):
            print(f"Data already exists for {symbol} on {date.date()}, skipping...")
            return
        print(f"Loading data for {symbol} on {date.date()}")
        data = self.download_data(symbol, date)
        self.save_data(data, symbol, date)

    def load_data_for_range(self, symbol: str, start_date: datetime, end_date: datetime) -> None:
        """Load data for a symbol over a date range."""
        date_range = [start_date + timedelta(days=x) for x in range((end_date - start_date).days + 1)]
        with ThreadPoolExecutor() as executor:
            futures = [executor.submit(self.load_data_for_date, symbol, single_date) for single_date in date_range]
            for future in futures:
                future.result()


class OrderBookLoader(BybitDataLoader):
    """Loader for ByBit orderbook data."""
    def __init__(self, output_base_dir: str = "data"):
        super().__init__(
            base_url="https://quote-saver.bycsi.com/orderbook/linear/{symbol}/{date}_{symbol}_ob500.data.zip",
            output_base_dir=output_base_dir,
            data_type="orderbook",
        )


class TradesLoader(BybitDataLoader):
    """Loader for ByBit trades data."""
    def __init__(self, output_base_dir: str = "data"):
        super().__init__(
            base_url="https://public.bybit.com/trading/{symbol}/{symbol}{date}.csv.gz",
            output_base_dir=output_base_dir,
            data_type="trades",
        )
        
    def save_data(self, content: bytes, symbol: str, date: datetime) -> None:
        """Save the downloaded trades data directly without any conversion."""
        date_str = date.strftime("%Y-%m-%d")
        output_path = os.path.join(self.output_base_dir, symbol, self.data_type)
        if not os.path.exists(output_path):
            os.makedirs(output_path)
            
        # Save with original filename format for trades
        file_path = os.path.join(output_path, f"{symbol}{date_str}.csv.gz")
        with open(file_path, "wb") as f:
            f.write(content)
        print(f"Raw trade data saved to {file_path} for {symbol}")

    def data_exists(self, symbol: str, date: datetime) -> bool:
        """Check if trades data already exists for the given symbol and date."""
        date_str = date.strftime("%Y-%m-%d")
        file_path = os.path.join(self.output_base_dir, symbol, self.data_type, f"{symbol}{date_str}.csv.gz")
        return os.path.exists(file_path)


def load_symbol_data(symbol: str, start_date: datetime, end_date: datetime) -> None:
    """
    Load both orderbook and trades data for a symbol over a date range.
    
    Args:
        symbol (str): Trading pair symbol (e.g., 'BTCUSDT')
        start_date (datetime): Start date for data download
        end_date (datetime): End date for data download
    """
    def load_orderbook():
        orderbook_loader = OrderBookLoader()
        orderbook_loader.load_data_for_range(symbol, start_date, end_date)
        print(f"Orderbook data loading completed for {symbol}")

    def load_trades():
        trades_loader = TradesLoader()
        trades_loader.load_data_for_range(symbol, start_date, end_date)
        print(f"Trades data loading completed for {symbol}")

    with ThreadPoolExecutor(max_workers=2) as executor:
        print(f"Loading data for {symbol}")
        #executor.submit(load_orderbook)
        executor.submit(load_trades)


if __name__ == "__main__":
    # Example usage
    symbol = "PAXGUSDT"
    start_date = datetime(2025, 4, 3)
    end_date = datetime(2025, 5, 11)
    
    load_symbol_data(symbol, start_date, end_date)
