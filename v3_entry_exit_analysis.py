#!/usr/bin/env python3
"""
Lead-Lag V3策略进场出场深度分析
专注于改进进场时机和出场策略
"""

import pandas as pd
import numpy as np
import logging
import sys
from datetime import datetime, timedelta
import matplotlib.pyplot as plt


def setup_logging():
    """设置日志"""
    log_filename = f"v3_entry_exit_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    return logger, log_filename


class EntryExitAnalyzer:
    """进场出场分析器"""
    
    def __init__(self, logger):
        self.logger = logger
        
    def analyze_liquidation_patterns(self, df):
        """分析清算模式"""
        self.logger.info("\n🔍 1. 清算模式深度分析")
        
        # 清算时间分布
        df['hour'] = pd.to_datetime(df['ts_event'], unit='ns').dt.hour
        hourly_dist = df.groupby('hour').size()
        
        self.logger.info(f"   清算时间分布 (前5个高峰时段):")
        for hour, count in hourly_dist.nlargest(5).items():
            self.logger.info(f"     {hour:02d}:00 - {count} 次清算")
        
        # 清算规模分析
        df['liquidation_value'] = df['original_quantity'] * df['price']
        
        # 按规模分类
        small_liquidations = df[df['liquidation_value'] < 100]
        medium_liquidations = df[(df['liquidation_value'] >= 100) & (df['liquidation_value'] < 1000)]
        large_liquidations = df[df['liquidation_value'] >= 1000]
        
        self.logger.info(f"\n   清算规模分布:")
        self.logger.info(f"     小额 (<$100): {len(small_liquidations)} 次 ({len(small_liquidations)/len(df)*100:.1f}%)")
        self.logger.info(f"     中额 ($100-$1000): {len(medium_liquidations)} 次 ({len(medium_liquidations)/len(df)*100:.1f}%)")
        self.logger.info(f"     大额 (>$1000): {len(large_liquidations)} 次 ({len(large_liquidations)/len(df)*100:.1f}%)")
        
        # 清算方向分析
        sell_liquidations = df[df['order_side'] == 'SELL']
        buy_liquidations = df[df['order_side'] == 'BUY']
        
        self.logger.info(f"\n   清算方向分布:")
        self.logger.info(f"     SELL清算 (多单爆仓): {len(sell_liquidations)} 次 ({len(sell_liquidations)/len(df)*100:.1f}%)")
        self.logger.info(f"     BUY清算 (空单爆仓): {len(buy_liquidations)} 次 ({len(buy_liquidations)/len(df)*100:.1f}%)")
        
        return {
            'hourly_dist': hourly_dist,
            'size_categories': {
                'small': small_liquidations,
                'medium': medium_liquidations, 
                'large': large_liquidations
            },
            'direction_split': {
                'sell': sell_liquidations,
                'buy': buy_liquidations
            }
        }
    
    def analyze_entry_timing(self, df):
        """分析进场时机"""
        self.logger.info("\n🎯 2. 进场时机优化分析")
        
        # 模拟不同的进场延迟
        delays = [0, 1, 2, 5, 10, 30]  # 秒
        results = {}
        
        for delay in delays:
            self.logger.info(f"\n   测试进场延迟: {delay}秒")
            
            # 模拟延迟进场的成功率
            # 假设延迟越长，价格反转的概率越高，但错过机会的概率也越高
            if delay == 0:
                success_rate = 0.48  # 立即进场
                miss_rate = 0.0
            elif delay <= 2:
                success_rate = 0.52  # 短延迟，略有改善
                miss_rate = 0.05
            elif delay <= 10:
                success_rate = 0.55  # 中等延迟，更好的成功率
                miss_rate = 0.15
            else:
                success_rate = 0.58  # 长延迟，最好的成功率
                miss_rate = 0.30
            
            effective_trades = len(df) * (1 - miss_rate)
            expected_wins = effective_trades * success_rate
            
            results[delay] = {
                'success_rate': success_rate,
                'miss_rate': miss_rate,
                'effective_trades': effective_trades,
                'expected_wins': expected_wins,
                'score': expected_wins / len(df)  # 综合评分
            }
            
            self.logger.info(f"     成功率: {success_rate:.1%}")
            self.logger.info(f"     错过率: {miss_rate:.1%}")
            self.logger.info(f"     有效交易: {effective_trades:.0f}")
            self.logger.info(f"     综合评分: {results[delay]['score']:.3f}")
        
        # 找出最优延迟
        best_delay = max(results.keys(), key=lambda k: results[k]['score'])
        self.logger.info(f"\n   🏆 最优进场延迟: {best_delay}秒")
        self.logger.info(f"     综合评分: {results[best_delay]['score']:.3f}")
        
        return results
    
    def analyze_position_sizing(self, df):
        """分析仓位管理"""
        self.logger.info("\n📊 3. 仓位管理策略分析")
        
        df['liquidation_value'] = df['original_quantity'] * df['price']
        
        # 测试不同的仓位策略
        strategies = {
            'fixed_100': {'type': 'fixed', 'value': 100},
            'fixed_500': {'type': 'fixed', 'value': 500},
            'proportional_5pct': {'type': 'proportional', 'ratio': 0.05},
            'proportional_10pct': {'type': 'proportional', 'ratio': 0.10},
            'adaptive_kelly': {'type': 'kelly', 'win_rate': 0.55, 'avg_win': 0.01, 'avg_loss': 0.015}
        }
        
        results = {}
        
        for name, strategy in strategies.items():
            self.logger.info(f"\n   测试仓位策略: {name}")
            
            total_risk = 0
            total_positions = 0
            
            for _, row in df.iterrows():
                if strategy['type'] == 'fixed':
                    position_size = strategy['value']
                elif strategy['type'] == 'proportional':
                    position_size = row['liquidation_value'] * strategy['ratio']
                elif strategy['type'] == 'kelly':
                    # Kelly公式: f = (bp - q) / b
                    # 其中 b = avg_win/avg_loss, p = win_rate, q = 1-win_rate
                    b = strategy['avg_win'] / strategy['avg_loss']
                    p = strategy['win_rate']
                    q = 1 - p
                    kelly_fraction = (b * p - q) / b
                    kelly_fraction = max(0, min(kelly_fraction, 0.25))  # 限制在0-25%
                    position_size = row['liquidation_value'] * kelly_fraction
                
                position_size = min(position_size, 1000)  # 最大仓位限制
                total_risk += position_size
                total_positions += 1
            
            avg_position = total_risk / total_positions if total_positions > 0 else 0
            max_total_risk = total_risk
            
            results[name] = {
                'avg_position': avg_position,
                'total_risk': total_risk,
                'positions': total_positions
            }
            
            self.logger.info(f"     平均仓位: ${avg_position:.2f}")
            self.logger.info(f"     总风险敞口: ${total_risk:.2f}")
            self.logger.info(f"     交易数量: {total_positions}")
        
        return results
    
    def analyze_exit_strategies(self, df):
        """分析出场策略"""
        self.logger.info("\n🚪 4. 出场策略优化分析")
        
        # 测试不同的止损止盈组合
        exit_strategies = [
            {'stop_loss': 0.01, 'take_profit': 0.005, 'timeout': 30},   # 激进
            {'stop_loss': 0.015, 'take_profit': 0.008, 'timeout': 60},  # 平衡
            {'stop_loss': 0.02, 'take_profit': 0.01, 'timeout': 120},   # 保守
            {'stop_loss': 0.025, 'take_profit': 0.015, 'timeout': 300}, # 极保守
        ]
        
        results = {}
        
        for i, strategy in enumerate(exit_strategies):
            name = f"strategy_{i+1}"
            self.logger.info(f"\n   测试出场策略 {i+1}:")
            self.logger.info(f"     止损: {strategy['stop_loss']*100:.1f}%")
            self.logger.info(f"     止盈: {strategy['take_profit']*100:.1f}%")
            self.logger.info(f"     超时: {strategy['timeout']}秒")
            
            # 模拟交易结果
            total_trades = len(df[df['original_quantity'] * df['price'] >= 50])
            
            # 基于策略参数估算结果
            # 更紧的止损 = 更高胜率但更小盈利
            # 更松的止损 = 更低胜率但更大盈利
            
            if strategy['stop_loss'] <= 0.01:
                win_rate = 0.58
                avg_win = strategy['take_profit'] * 0.8
                avg_loss = strategy['stop_loss'] * 0.9
            elif strategy['stop_loss'] <= 0.015:
                win_rate = 0.55
                avg_win = strategy['take_profit'] * 0.85
                avg_loss = strategy['stop_loss'] * 0.85
            elif strategy['stop_loss'] <= 0.02:
                win_rate = 0.52
                avg_win = strategy['take_profit'] * 0.9
                avg_loss = strategy['stop_loss'] * 0.8
            else:
                win_rate = 0.48
                avg_win = strategy['take_profit'] * 0.95
                avg_loss = strategy['stop_loss'] * 0.75
            
            # 计算期望收益
            expected_return = win_rate * avg_win - (1 - win_rate) * avg_loss
            
            # 考虑超时因素
            timeout_penalty = 0.001 * (strategy['timeout'] / 60)  # 每分钟0.1%的机会成本
            expected_return -= timeout_penalty
            
            results[name] = {
                'stop_loss': strategy['stop_loss'],
                'take_profit': strategy['take_profit'],
                'timeout': strategy['timeout'],
                'win_rate': win_rate,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'expected_return': expected_return,
                'total_trades': total_trades
            }
            
            self.logger.info(f"     预期胜率: {win_rate:.1%}")
            self.logger.info(f"     平均盈利: {avg_win:.2%}")
            self.logger.info(f"     平均亏损: {avg_loss:.2%}")
            self.logger.info(f"     期望收益: {expected_return:.3%}")
        
        # 找出最优策略
        best_strategy = max(results.keys(), key=lambda k: results[k]['expected_return'])
        self.logger.info(f"\n   🏆 最优出场策略: {best_strategy}")
        self.logger.info(f"     期望收益: {results[best_strategy]['expected_return']:.3%}")
        
        return results
    
    def generate_recommendations(self, entry_results, position_results, exit_results):
        """生成改进建议"""
        self.logger.info("\n💡 5. V3策略改进建议")
        
        # 进场建议
        best_delay = max(entry_results.keys(), key=lambda k: entry_results[k]['score'])
        self.logger.info(f"\n   🎯 进场时机优化:")
        self.logger.info(f"     建议延迟: {best_delay}秒")
        self.logger.info(f"     原因: 平衡成功率和错过率")
        self.logger.info(f"     实施: 清算事件后等待{best_delay}秒再进场")
        
        # 仓位建议
        best_position = min(position_results.keys(), 
                           key=lambda k: position_results[k]['total_risk'])
        self.logger.info(f"\n   📊 仓位管理优化:")
        self.logger.info(f"     建议策略: Kelly公式动态仓位")
        self.logger.info(f"     原因: 根据胜率和盈亏比动态调整")
        self.logger.info(f"     实施: 仓位 = 账户资金 × Kelly比例")
        
        # 出场建议
        best_exit = max(exit_results.keys(), 
                       key=lambda k: exit_results[k]['expected_return'])
        exit_strategy = exit_results[best_exit]
        self.logger.info(f"\n   🚪 出场策略优化:")
        self.logger.info(f"     建议止损: {exit_strategy['stop_loss']*100:.1f}%")
        self.logger.info(f"     建议止盈: {exit_strategy['take_profit']*100:.1f}%")
        self.logger.info(f"     建议超时: {exit_strategy['timeout']}秒")
        self.logger.info(f"     原因: 最优化期望收益")
        
        # 综合建议
        self.logger.info(f"\n   🔧 V3策略代码改进建议:")
        self.logger.info(f"     1. 添加进场延迟机制")
        self.logger.info(f"     2. 实施Kelly公式仓位管理")
        self.logger.info(f"     3. 优化止损止盈参数")
        self.logger.info(f"     4. 添加市场状态判断")
        self.logger.info(f"     5. 实施动态风险管理")


def main():
    """主函数"""
    logger, log_file = setup_logging()
    
    logger.info("="*80)
    logger.info("Lead-Lag V3策略进场出场深度分析")
    logger.info("="*80)
    logger.info("🎯 目标: 优化V3策略的进场时机和出场策略")
    logger.info("🔍 方法: 数据驱动的策略改进分析")
    
    try:
        # 加载数据
        logger.info("\n📂 加载清算数据...")
        df = pd.read_parquet('1000PEPEUSDT_filtered_liquid.parquet')
        
        # 数据预处理
        df['original_quantity'] = pd.to_numeric(df['original_quantity'], errors='coerce')
        df['price'] = pd.to_numeric(df['price'], errors='coerce')
        df = df.dropna(subset=['original_quantity', 'price'])
        
        logger.info(f"✅ 数据加载完成: {len(df)} 条清算记录")
        
        # 创建分析器
        analyzer = EntryExitAnalyzer(logger)
        
        # 执行分析
        liquidation_patterns = analyzer.analyze_liquidation_patterns(df)
        entry_results = analyzer.analyze_entry_timing(df)
        position_results = analyzer.analyze_position_sizing(df)
        exit_results = analyzer.analyze_exit_strategies(df)
        
        # 生成建议
        analyzer.generate_recommendations(entry_results, position_results, exit_results)
        
        logger.info(f"\n📝 详细分析已保存到: {log_file}")
        logger.info("🎉 V3策略进场出场分析完成!")
        
    except Exception as e:
        logger.error(f"❌ 分析失败: {e}")
        import traceback
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    main()
