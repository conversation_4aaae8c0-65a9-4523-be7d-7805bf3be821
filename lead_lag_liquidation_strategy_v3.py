"""
Lead-Lag清算跟随策略 V3.0 - 反向清算策略
基于深度回测分析的重构版本

核心改进:
1. 反向清算逻辑 - 清算通常预示反转
2. 多因子信号确认 - 提高信号质量
3. 动态风险管理 - 自适应止损和仓位
4. 智能订单执行 - Maker策略降低成本
"""

from decimal import Decimal
from typing import Dict, Any, Optional
import pandas as pd
import numpy as np

from nautilus_trader.core.message import Event
from nautilus_trader.model.data import TradeTick
from nautilus_trader.model.enums import OrderSide, TimeInForce
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.instruments import CryptoFuture
from nautilus_trader.model.orders import MarketOrder, LimitOrder
from nautilus_trader.trading.strategy import Strategy

from nautilus_trader.adapters.binance.futures.types import BinanceFuturesLiquidationOrder


class LeadLagConfig:
    """优化的策略配置"""
    def __init__(
        self,
        binance_instrument_id: InstrumentId,
        bybit_instrument_id: InstrumentId,
        # 核心参数 - 更保守的设置
        min_liquidation_value_usdt: float = 50.0,      # 提高最小清算价值
        min_trade_value_usdt: float = 10.0,            # 提高最小交易价值
        max_position_value_usdt: float = 1000.0,       # 降低最大仓位
        signal_strength_threshold: float = 0.02,       # 提高信号阈值
        
        # 风险管理 - 更严格的控制
        stop_loss_pct: float = 0.02,                   # 收紧止损到2%
        take_profit_pct: float = 0.01,                 # 降低止盈到1%
        position_timeout_seconds: int = 60,            # 缩短到1分钟
        max_drawdown_pct: float = 0.03,                # 最大回撤3%
        
        # 执行参数
        ttl_ms: int = 2000,                            # 2秒TTL
        ema_period: int = 20,                          # 增加EMA周期
        
        # 新增参数
        reverse_strategy: bool = True,                  # 启用反向策略
        multi_factor_confirmation: bool = True,         # 多因子确认
        dynamic_risk_management: bool = True,           # 动态风险管理
        maker_only: bool = True,                       # 仅使用Maker订单
    ):
        self.binance_instrument_id = binance_instrument_id
        self.bybit_instrument_id = bybit_instrument_id
        self.min_liquidation_value_usdt = min_liquidation_value_usdt
        self.min_trade_value_usdt = min_trade_value_usdt
        self.max_position_value_usdt = max_position_value_usdt
        self.signal_strength_threshold = signal_strength_threshold
        self.stop_loss_pct = stop_loss_pct
        self.take_profit_pct = take_profit_pct
        self.position_timeout_seconds = position_timeout_seconds
        self.max_drawdown_pct = max_drawdown_pct
        self.ttl_ms = ttl_ms
        self.ema_period = ema_period
        self.reverse_strategy = reverse_strategy
        self.multi_factor_confirmation = multi_factor_confirmation
        self.dynamic_risk_management = dynamic_risk_management
        self.maker_only = maker_only


class LeadLagLiquidationStrategyV3(Strategy):
    """
    Lead-Lag清算跟随策略 V3.0
    
    核心逻辑:
    1. 监听Binance清算事件
    2. 反向操作 - 清算通常预示反转
    3. 多因子确认信号质量
    4. 动态风险管理
    5. 在Bybit执行Maker订单
    """
    
    def __init__(self, config: LeadLagConfig):
        super().__init__()
        self.config = config
        
        # 交易对和工具
        self.binance_instrument_id = config.binance_instrument_id
        self.bybit_instrument_id = config.bybit_instrument_id
        self.binance_instrument: Optional[CryptoFuture] = None
        self.bybit_instrument: Optional[CryptoFuture] = None
        
        # 价格数据
        self.binance_prices = []
        self.bybit_prices = []
        self.price_history = pd.DataFrame()
        
        # 风险管理
        self.total_pnl = 0.0
        self.max_drawdown = 0.0
        self.peak_equity = 100000.0
        self.recent_trades = []
        self.trading_enabled = True
        
        # 统计数据
        self.liquidation_count = 0
        self.signal_count = 0
        self.trade_count = 0
        self.win_count = 0
        
        # 订单管理
        self.pending_orders: Dict[str, Dict] = {}
        self.active_positions = []

    def on_start(self):
        """策略启动"""
        print("🚀 启动Lead-Lag清算跟随策略 V3.0...")
        print("🔄 核心改进: 反向清算 + 多因子确认 + 动态风险管理")
        
        # 获取交易工具
        self.binance_instrument = self.cache.instrument(self.binance_instrument_id)
        self.bybit_instrument = self.cache.instrument(self.bybit_instrument_id)
        
        if not self.binance_instrument or not self.bybit_instrument:
            self.log.error("无法获取交易工具信息")
            return
            
        print(f"📊 监听交易对: {self.binance_instrument_id} -> {self.bybit_instrument_id}")
        print(f"🎯 反向策略: {'启用' if self.config.reverse_strategy else '禁用'}")
        print(f"🔍 多因子确认: {'启用' if self.config.multi_factor_confirmation else '禁用'}")
        print(f"🛡️ 动态风险管理: {'启用' if self.config.dynamic_risk_management else '禁用'}")
        
        # 订阅数据
        self.subscribe_data(
            data_type=BinanceFuturesLiquidationOrder,
            client_id=None,
            metadata={"instrument_id": self.binance_instrument_id}
        )
        
        self.subscribe_trade_ticks(self.binance_instrument_id)
        self.subscribe_trade_ticks(self.bybit_instrument_id)
        
        print("✅ Lead-Lag策略 V3.0 启动完成")

    def on_data(self, data):
        """处理数据事件"""
        if isinstance(data, BinanceFuturesLiquidationOrder):
            self._handle_liquidation(data)
        elif isinstance(data, TradeTick):
            self._handle_trade_tick(data)

    def _handle_liquidation(self, liquidation: BinanceFuturesLiquidationOrder):
        """处理清算事件 - V3.0 反向逻辑"""
        self.liquidation_count += 1
        
        # 基础过滤
        liquidation_value = float(liquidation.quantity) * float(liquidation.price)
        
        print(f"💥 清算事件 #{self.liquidation_count}: {liquidation.order_side} "
              f"{liquidation.quantity} @ {liquidation.price}")
        print(f"💰 清算价值: ${liquidation_value:.2f}")
        
        # 检查交易状态
        if not self.trading_enabled:
            print("❌ 交易已暂停 (风险保护)")
            return
            
        # 价值过滤
        if liquidation_value < self.config.min_liquidation_value_usdt:
            print(f"❌ 清算价值过小: ${liquidation_value:.2f} < ${self.config.min_liquidation_value_usdt}")
            return
            
        # 获取当前价格
        current_bybit_price = self._get_current_price(self.bybit_instrument_id)
        if not current_bybit_price:
            print("❌ 无法获取Bybit当前价格")
            return
            
        # 计算信号强度
        signal_strength = liquidation_value / 10000.0  # 标准化
        
        if signal_strength < self.config.signal_strength_threshold:
            print(f"❌ 信号强度不足: {signal_strength:.4f} < {self.config.signal_strength_threshold}")
            return
            
        print(f"✅ 信号强度充足: {signal_strength:.4f}")
        
        # V3.0 核心改进: 反向清算策略
        if self.config.reverse_strategy:
            # 反向逻辑: 清算通常预示反转
            if liquidation.order_side == OrderSide.SELL:
                # 多单被清算 -> 可能超跌 -> 做多
                trade_side = OrderSide.BUY
                print("🔄 反向策略: 多单清算 -> 做多 (抄底)")
            else:
                # 空单被清算 -> 可能超涨 -> 做空  
                trade_side = OrderSide.SELL
                print("🔄 反向策略: 空单清算 -> 做空 (做顶)")
        else:
            # 原始跟随逻辑
            trade_side = liquidation.order_side
            print(f"➡️ 跟随策略: 清算方向 {liquidation.order_side}")
            
        # 多因子确认 (如果启用)
        if self.config.multi_factor_confirmation:
            if not self._multi_factor_confirmation(liquidation, current_bybit_price, trade_side):
                print("❌ 多因子确认失败")
                return
                
        # 动态仓位计算
        position_size = self._calculate_dynamic_position_size(liquidation_value, signal_strength)
        
        if position_size <= 0:
            print("❌ 计算的仓位大小无效")
            return
            
        # 生成交易信号
        signal = {
            'side': trade_side,
            'quantity': position_size,
            'liquidation_value': liquidation_value,
            'signal_strength': signal_strength,
            'strategy_version': 'V3.0',
            'reverse_strategy': self.config.reverse_strategy
        }
        
        print(f"🎯 V3.0交易信号: {trade_side} {position_size} (反向策略: {self.config.reverse_strategy})")
        
        # 执行交易
        self._execute_trade(signal, current_bybit_price)

    def _multi_factor_confirmation(self, liquidation, current_price, trade_side) -> bool:
        """多因子信号确认"""
        confirmation_score = 0
        
        # 因子1: 清算规模 (大额清算更可靠)
        liquidation_value = float(liquidation.quantity) * float(liquidation.price)
        if liquidation_value > 100:  # 大于100 USDT
            confirmation_score += 1
            
        # 因子2: 价格偏离度 (TODO: 需要更多历史数据)
        # 这里简化处理
        confirmation_score += 1
        
        # 因子3: 成交量确认 (TODO: 需要成交量数据)
        confirmation_score += 1
        
        # 需要至少2个因子确认
        confirmed = confirmation_score >= 2
        print(f"🔍 多因子确认: {confirmation_score}/3 -> {'通过' if confirmed else '失败'}")
        
        return confirmed

    def _calculate_dynamic_position_size(self, liquidation_value: float, signal_strength: float) -> int:
        """动态仓位计算"""
        # 基础仓位
        base_position_value = min(liquidation_value * 0.1, self.config.max_position_value_usdt)
        
        # 信号强度调整
        signal_multiplier = min(signal_strength * 2, 2.0)
        adjusted_position_value = base_position_value * signal_multiplier
        
        # 风险调整 (如果启用动态风险管理)
        if self.config.dynamic_risk_management:
            risk_multiplier = self._get_risk_multiplier()
            adjusted_position_value *= risk_multiplier
            
        # 转换为数量
        current_price = self._get_current_price(self.bybit_instrument_id)
        if not current_price:
            return 0
            
        quantity = int(adjusted_position_value / current_price)
        
        # 确保最小交易量
        min_quantity = int(self.config.min_trade_value_usdt / current_price)
        quantity = max(quantity, min_quantity)
        
        print(f"📊 动态仓位: 基础${base_position_value:.0f} × 信号{signal_multiplier:.1f} = {quantity}")
        
        return quantity

    def _get_risk_multiplier(self) -> float:
        """获取风险调整倍数"""
        # 基于最近胜率调整
        if len(self.recent_trades) >= 10:
            recent_win_rate = sum(1 for trade in self.recent_trades[-10:] if trade > 0) / 10
            if recent_win_rate < 0.3:
                return 0.5  # 胜率低时减少仓位
            elif recent_win_rate > 0.7:
                return 1.2  # 胜率高时增加仓位
                
        # 基于回撤调整
        current_drawdown = (self.peak_equity - (self.peak_equity + self.total_pnl)) / self.peak_equity
        if current_drawdown > self.config.max_drawdown_pct:
            self.trading_enabled = False
            print(f"🚨 触发最大回撤保护: {current_drawdown:.2%}")
            return 0.0
            
        return 1.0

    def _execute_trade(self, signal: Dict[str, Any], current_price: float):
        """执行交易 - V3.0 Maker策略"""
        if not self.trading_enabled:
            return
            
        side = signal['side']
        quantity = self.bybit_instrument.make_qty(signal['quantity'])
        
        # V3.0改进: 使用限价单 (Maker)
        if self.config.maker_only:
            # 计算有利价格 (提供流动性)
            if side == OrderSide.BUY:
                # 买单价格略低于市价
                order_price = current_price * 0.9995  # 0.05%折扣
            else:
                # 卖单价格略高于市价  
                order_price = current_price * 1.0005  # 0.05%溢价
                
            price = self.bybit_instrument.make_price(order_price)
            
            # 创建限价单
            from datetime import timedelta
            expire_time = self.clock.utc_now() + timedelta(milliseconds=self.config.ttl_ms)
            
            order = self.order_factory.limit(
                instrument_id=self.bybit_instrument_id,
                order_side=side,
                quantity=quantity,
                price=price,
                time_in_force=TimeInForce.GTD,
                expire_time=expire_time,
                post_only=True,  # 确保Maker
            )
            
            print(f"📤 V3.0限价单: {side} {quantity} @ {price} (Maker)")
        else:
            # 市价单 (Taker)
            order = self.order_factory.market(
                instrument_id=self.bybit_instrument_id,
                order_side=side,
                quantity=quantity,
            )
            print(f"📤 V3.0市价单: {side} {quantity} @ MARKET (Taker)")
            
        # 记录订单
        self.pending_orders[order.client_order_id] = {
            'signal': signal,
            'submit_time': self.clock.timestamp_ns(),
            'order': order
        }
        
        # 提交订单
        self.submit_order(order)
        self.signal_count += 1

    def _handle_trade_tick(self, tick: TradeTick):
        """处理价格数据"""
        # 更新价格历史
        if tick.instrument_id == self.binance_instrument_id:
            self.binance_prices.append(float(tick.price))
            if len(self.binance_prices) > 1000:
                self.binance_prices = self.binance_prices[-1000:]
        elif tick.instrument_id == self.bybit_instrument_id:
            self.bybit_prices.append(float(tick.price))
            if len(self.bybit_prices) > 1000:
                self.bybit_prices = self.bybit_prices[-1000:]

    def _get_current_price(self, instrument_id: InstrumentId) -> Optional[float]:
        """获取当前价格"""
        if instrument_id == self.binance_instrument_id and self.binance_prices:
            return self.binance_prices[-1]
        elif instrument_id == self.bybit_instrument_id and self.bybit_prices:
            return self.bybit_prices[-1]
        return None

    def on_order_filled(self, order_filled):
        """订单成交处理"""
        self.trade_count += 1
        
        # 更新PnL跟踪
        if hasattr(order_filled, 'last_px') and hasattr(order_filled, 'last_qty'):
            trade_value = float(order_filled.last_px) * float(order_filled.last_qty)
            
            # 简化的PnL计算 (实际应该更复杂)
            if order_filled.order_side == OrderSide.SELL:
                self.total_pnl += trade_value * 0.001  # 假设0.1%收益
            else:
                self.total_pnl -= trade_value * 0.001
                
            self.recent_trades.append(trade_value * 0.001)
            if len(self.recent_trades) > 50:
                self.recent_trades = self.recent_trades[-50:]
                
            # 更新峰值权益
            current_equity = self.peak_equity + self.total_pnl
            if current_equity > self.peak_equity:
                self.peak_equity = current_equity
                
        print(f"🎉 V3.0订单成交: {order_filled.client_order_id}")
        print(f"📊 累计PnL: {self.total_pnl:.2f} USDT")

    def on_stop(self):
        """策略停止"""
        print("\n" + "="*80)
        print("📊 Lead-Lag策略 V3.0 回测完成")
        print("="*80)
        print(f"📈 清算事件: {self.liquidation_count}")
        print(f"🎯 生成信号: {self.signal_count}")
        print(f"💼 执行交易: {self.trade_count}")
        print(f"💰 累计PnL: {self.total_pnl:.2f} USDT")
        print(f"📊 胜率: {(self.win_count/max(self.trade_count,1)*100):.1f}%")
        print("="*80)
