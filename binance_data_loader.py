import os
import pandas as pd
import requests
import json
# from binance.client import Client
from io import BytesIO
from zipfile import ZipFile
from datetime import datetime, timedelta
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Optional, List, Union
import numpy as np

class BinanceDataLoader:
    def __init__(self, base_url, output_base_dir, data_type):
        self.base_url = base_url
        self.output_base_dir = output_base_dir
        self.data_type = data_type

    def data_exists(self, symbol, date):
        date_str = date.strftime("%Y-%m-%d")
        parquet_path= os.path.join(self.output_base_dir, symbol, self.data_type, f"{symbol}-{self.data_type}-{date_str}.parquet")
        return os.path.exists(parquet_path)

    def download_data(self, symbol, date):
        formatted_date = date.strftime("%Y-%m-%d")
        url = self.base_url.format(symbol=symbol, date=formatted_date)
        response = requests.get(url)
        response.raise_for_status()
        return response.content

    def extract_and_convert(self, zip_content, symbol, date):
        date_str = date.strftime("%Y-%m-%d")
        output_path = os.path.join(self.output_base_dir, symbol, self.data_type)
        if not os.path.exists(output_path):
            os.makedirs(output_path)
        with ZipFile(BytesIO(zip_content)) as zip_file:
            csv_filename = zip_file.namelist()[0]
            zip_file.extract(csv_filename, path=output_path)

            csv_path = os.path.join(output_path, csv_filename)
            new_csv_path = os.path.join(output_path, f"{symbol}-{self.data_type}-{date_str}.csv")
            os.rename(csv_path, new_csv_path)

            df = pd.read_csv(new_csv_path)
            parquet_path= os.path.splitext(new_csv_path)[0] + '.parquet'
            df.to_parquet(parquet_path)
            print(f"Data saved in parquet format to {parquet_path} for {symbol}")

            os.remove(new_csv_path)

    def load_data_for_date(self, symbol, date):
        if self.data_exists(symbol, date):
            print(f"Data already exists for {symbol} on {date.date()}, skipping...")
            return
        print(f"Loading data for {symbol} on {date.date()}")
        data = self.download_data(symbol, date)
        self.extract_and_convert(data, symbol, date)

    def load_data_for_range(self, symbol, start_date, end_date):
        date_range = [start_date + timedelta(days=x) for x in range((end_date - start_date).days + 1)]
        with ThreadPoolExecutor() as executor:
            futures = [executor.submit(self.load_data_for_date, symbol, single_date) for single_date in date_range]
            for future in futures:
                future.result()

class TradesLoader(BinanceDataLoader):
    def __init__(self, output_base_dir="data"):
        super().__init__("https://data.binance.vision/data/futures/um/daily/trades/{symbol}/{symbol}-trades-{date}.zip", output_base_dir, "trades")

class BookTickerLoader(BinanceDataLoader):
    def __init__(self, output_base_dir="data"):
        super().__init__("https://data.binance.vision/data/futures/um/daily/bookTicker/{symbol}/{symbol}-bookTicker-{date}.zip", output_base_dir, "quotes")


class LiquidationSnapshotLoader(BinanceDataLoader):
    def __init__(self, output_base_dir="data"):
        super().__init__("https://data.binance.vision/data/futures/um/daily/liquidationSnapshot/{symbol}/{symbol}-liquidationSnapshot-{date}.zip", output_base_dir, "liquidationSnapshot")

def load_symbol_data(symbol, start_date, end_date):
    def load_trades():
        trades_loader = TradesLoader()
        trades_loader.load_data_for_range(symbol, start_date, end_date)
        print(f"Trades data loading completed for {symbol}")

    def load_book_tickers():
        book_ticker_loader = BookTickerLoader()
        book_ticker_loader.load_data_for_range(symbol, start_date, end_date)
        print(f"Book ticker data loading completed for {symbol}")

    def load_liquidation_snapshots():
        liquidation_snapshot_loader = LiquidationSnapshotLoader()
        liquidation_snapshot_loader.load_data_for_range(symbol, start_date, end_date)
        print(f"Liquidation snapshot data loading completed for {symbol}")

    with ThreadPoolExecutor(max_workers=3) as executor:
        print("Loading trades")
        executor.submit(load_trades)
        print("Loading liquidation snapshots")
        executor.submit(load_liquidation_snapshots)

# class BarDataLoader(BinanceDataLoader):
#     """
#     Bar data loader for downloading OHLCV data from Binance using official client.
    
#     Attributes:
#         output_base_dir (str): Base directory for data storage
#         client (Client): Binance API client instance
#     """
    
#     def __init__(self, output_base_dir: str = "data"):
#         """
#         Initialize BarDataLoader with Binance API credentials from config file.
        
#         Args:
#             output_base_dir (str): Base directory for storing data
#         """
#         super().__init__("", output_base_dir, "bars")
#         with open("config.json", 'r') as f:
#             api_key_data = json.load(f)
#         self.client = Client(api_key_data['api_key'], api_key_data['api_secret'])

#     def download_bars(
#         self, 
#         symbol: str,
#         interval: str = "1d",
#         start_date: Optional[datetime] = None,
#         end_date: Optional[datetime] = None
#     ) -> pd.DataFrame:
#         """
#         Download historical kline/candlestick data for a given symbol and timeframe.
        
#         Args:
#             symbol (str): Trading pair symbol (e.g., 'BTCUSDT')
#             interval (str): Kline interval. Options: 1m,3m,5m,15m,30m,1h,2h,4h,6h,8h,12h,1d,3d,1w,1M
#             start_date (datetime, optional): Start date for data download
#             end_date (datetime, optional): End date for data download
            
#         Returns:
#             pd.DataFrame: DataFrame containing all Binance bar data with columns:
#                          [timestamp, open, high, low, close, volume, close_time, 
#                           quote_volume, count, taker_buy_base_volume, taker_buy_quote_volume,
#                           taker_sell_base_volume, taker_sell_quote_volume]
#                          All timestamps are in milliseconds
#         Example:
#             >>> loader = BarDataLoader()
#             >>> # Download 5-minute bars for the last 2 days
#             >>> start = datetime.now() - timedelta(days=2)
#             >>> df = loader.download_bars("BTCUSDT", "5m", start_date=start)
#             >>> print(df.head())
#                     timestamp        open        high         low       close     volume  close_time  quote_volume  count  taker_buy_base_vol  taker_buy_quote_vol
#             0  1711238400000    70123.45    70245.67    70100.23    70198.34   123.456  1711238459999    8650000.0    1200            75.234           5270000.0
#         """
#         start_str = start_date.strftime("%d %b %Y") if start_date else "1 Jan 2017"
#         end_str = end_date.strftime("%d %b %Y") if end_date else None
        
#         klines = self.client.get_historical_klines(
#             symbol=symbol,
#             interval=interval,
#             start_str=start_str,
#             end_str=end_str
#         )
        
#         # Define all columns according to Binance API documentation
#         df = pd.DataFrame(klines, columns=[
#             "open_time",           # Open time
#             "open",               # Open
#             "high",               # High
#             "low",                # Low
#             "close",              # Close
#             "volume",             # Volume
#             "close_time",         # Close time
#             "quote_volume",       # Quote asset volume
#             "count",              # Number of trades
#             "taker_buy_base_volume",    # Taker buy base asset volume
#             "taker_buy_quote_volume",   # Taker buy quote asset volume
#             "ignore"              # Ignore
#         ])
        
#         # Convert numeric columns with appropriate types
#         price_cols = ["open", "high", "low", "close"]
#         volume_cols = ["volume", "quote_volume", "taker_buy_base_volume", "taker_buy_quote_volume"]
        
#         df[price_cols] = df[price_cols].astype(float)
#         df[volume_cols] = df[volume_cols].astype(float)
#         df["count"] = df["count"].astype(int)
#         df["open_time"] = df["open_time"].astype(np.int64)
#         df["close_time"] = df["close_time"].astype(np.int64)
        
#         # Calculate additional fields
#         df["taker_sell_base_volume"] = df["volume"] - df["taker_buy_base_volume"]
#         df["taker_sell_quote_volume"] = df["quote_volume"] - df["taker_buy_quote_volume"]
        
#         # Drop the ignore column
#         df = df.drop(columns=["ignore"])
        
#         # Save to parquet
#         output_path = os.path.join(self.output_base_dir, symbol, self.data_type)
#         if not os.path.exists(output_path):
#             os.makedirs(output_path)
            
#         date_str = datetime.now().strftime("%Y-%m-%d")
#         parquet_path = os.path.join(output_path, f"{symbol}-{interval}-{date_str}.parquet")
#         df.to_parquet(parquet_path)
#         print(f"Bar data saved to {parquet_path}")
        
#         return df

# if __name__ == "__main__":
#     # Example of downloading bar data
#     loader = BarDataLoader()
    
#     # Example 1: Download 5-minute bars for the last day
#     start_date = datetime.now() - timedelta(days=1)
#     bars_1m = loader.download_bars(
#         symbol="XVGUSDT",
#         interval="1m",
#         start_date= datetime(2023, 7, 5),
#         end_date=datetime(2024,3,31)
#     )
#     print("\nExample 1 - Last day's 5-minute bars with all fields:")
#     print(bars_1m.head().to_string())
    
   

    # start_date = datetime(2024, 3, 24)
    # end_date = datetime(2024, 3, 30)
    # symbols = ["1000BONKUSDT"]
    # start_time = time.time()

    # with ThreadPoolExecutor() as executor:
    #     futures = [executor.submit(load_symbol_data, symbol, start_date, end_date) for symbol in symbols]
        
    #     for future in futures:
    #         try:
    #             future.result()
    #         except Exception as e:
    #             print(f"An error occurred: {e}")

    # end_time = time.time()
    # total_time = end_time - start_time

    # print(f"All data loading tasks for all symbols completed in {total_time:.2f} seconds.")