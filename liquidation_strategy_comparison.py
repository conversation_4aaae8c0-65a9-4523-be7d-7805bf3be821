#!/usr/bin/env python3
"""
清算事件策略比较分析
比较两种策略思路：
1. 清算跟随策略：清算事件会造成Bybit跟随Binance
2. 清算回归策略：清算发生后会出现价格回归
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class LiquidationStrategyComparison:
    """清算策略比较分析器"""
    
    def __init__(self, symbol='WLDUSDT'):
        self.symbol = symbol
        self.results = {}
        
    def load_analysis_data(self):
        """加载分析数据"""
        try:
            # 加载增强分析结果
            enhanced_file = f"{self.symbol}_enhanced_liquidation_analysis.csv"
            self.enhanced_df = pd.read_csv(enhanced_file)
            self.enhanced_df['entry_time'] = pd.to_datetime(self.enhanced_df['entry_time'])
            
            print(f"加载了 {len(self.enhanced_df)} 个清算事件的增强分析数据")
            return True
            
        except FileNotFoundError:
            print(f"未找到 {enhanced_file} 文件")
            return False
    
    def analyze_follow_strategy(self):
        """
        分析跟随策略：清算事件会造成Bybit跟随Binance
        
        策略逻辑：
        - 当Binance发生大额清算时，预期Bybit价格会跟随变动
        - 如果清算是SELL（价格下跌），在Bybit做空
        - 如果清算是BUY（价格上涨），在Bybit做多
        """
        print("\n=== 分析跟随策略 ===")
        
        follow_results = []
        
        for idx, row in self.enhanced_df.iterrows():
            # 策略信号生成
            liquidation_side = row['liquidation_side']
            liquidation_quantity = row['liquidation_quantity']
            
            # 只考虑大额清算（>100）
            if liquidation_quantity < 100:
                continue
                
            # 跟随策略逻辑
            if liquidation_side == 'SELL':
                # 预期Bybit下跌，做空Bybit
                expected_direction = 'SHORT'
                actual_return = -row['actual_return_bybit_10s']  # 做空收益
            else:  # BUY
                # 预期Bybit上涨，做多Bybit
                expected_direction = 'LONG'
                actual_return = row['actual_return_bybit_10s']  # 做多收益
            
            # 考虑交易成本（0.04% taker费用）
            trading_cost = 0.0004 * 2  # 开仓+平仓
            net_return = actual_return - trading_cost
            
            follow_results.append({
                'entry_time': row['entry_time'],
                'liquidation_side': liquidation_side,
                'liquidation_quantity': liquidation_quantity,
                'expected_direction': expected_direction,
                'raw_return': actual_return,
                'net_return': net_return,
                'signal_strength': row['overall_signal_strength'],
                'correlation': row['max_correlation'],
                'lag': row['optimal_lag']
            })
        
        self.follow_df = pd.DataFrame(follow_results)
        
        if len(self.follow_df) > 0:
            # 计算策略表现
            total_trades = len(self.follow_df)
            winning_trades = len(self.follow_df[self.follow_df['net_return'] > 0])
            win_rate = winning_trades / total_trades
            
            avg_return = self.follow_df['net_return'].mean()
            total_return = self.follow_df['net_return'].sum()
            sharpe_ratio = avg_return / self.follow_df['net_return'].std() if self.follow_df['net_return'].std() > 0 else 0
            
            max_drawdown = self._calculate_max_drawdown(self.follow_df['net_return'].cumsum())
            
            self.results['follow_strategy'] = {
                'total_trades': total_trades,
                'win_rate': win_rate,
                'avg_return': avg_return,
                'total_return': total_return,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'best_trade': self.follow_df['net_return'].max(),
                'worst_trade': self.follow_df['net_return'].min()
            }
            
            print(f"跟随策略结果:")
            print(f"  总交易次数: {total_trades}")
            print(f"  胜率: {win_rate:.2%}")
            print(f"  平均收益: {avg_return:.4f}")
            print(f"  总收益: {total_return:.4f}")
            print(f"  夏普比率: {sharpe_ratio:.3f}")
            print(f"  最大回撤: {max_drawdown:.4f}")
        
        return self.follow_df
    
    def analyze_reversion_strategy(self):
        """
        分析回归策略：清算发生后会出现价格回归
        
        策略逻辑：
        - 清算事件造成价格偏离，随后会回归
        - 如果清算是SELL（价格下跌），预期价格反弹，做多
        - 如果清算是BUY（价格上涨），预期价格回调，做空
        """
        print("\n=== 分析回归策略 ===")
        
        reversion_results = []
        
        for idx, row in self.enhanced_df.iterrows():
            liquidation_side = row['liquidation_side']
            liquidation_quantity = row['liquidation_quantity']
            
            # 只考虑大额清算
            if liquidation_quantity < 100:
                continue
            
            # 回归策略逻辑（与跟随策略相反）
            if liquidation_side == 'SELL':
                # 清算导致下跌，预期反弹，做多Bybit
                expected_direction = 'LONG'
                actual_return = row['actual_return_bybit_10s']  # 做多收益
            else:  # BUY
                # 清算导致上涨，预期回调，做空Bybit
                expected_direction = 'SHORT'
                actual_return = -row['actual_return_bybit_10s']  # 做空收益
            
            # 考虑交易成本
            trading_cost = 0.0004 * 2
            net_return = actual_return - trading_cost
            
            reversion_results.append({
                'entry_time': row['entry_time'],
                'liquidation_side': liquidation_side,
                'liquidation_quantity': liquidation_quantity,
                'expected_direction': expected_direction,
                'raw_return': actual_return,
                'net_return': net_return,
                'signal_strength': row['overall_signal_strength'],
                'correlation': row['max_correlation'],
                'lag': row['optimal_lag']
            })
        
        self.reversion_df = pd.DataFrame(reversion_results)
        
        if len(self.reversion_df) > 0:
            # 计算策略表现
            total_trades = len(self.reversion_df)
            winning_trades = len(self.reversion_df[self.reversion_df['net_return'] > 0])
            win_rate = winning_trades / total_trades
            
            avg_return = self.reversion_df['net_return'].mean()
            total_return = self.reversion_df['net_return'].sum()
            sharpe_ratio = avg_return / self.reversion_df['net_return'].std() if self.reversion_df['net_return'].std() > 0 else 0
            
            max_drawdown = self._calculate_max_drawdown(self.reversion_df['net_return'].cumsum())
            
            self.results['reversion_strategy'] = {
                'total_trades': total_trades,
                'win_rate': win_rate,
                'avg_return': avg_return,
                'total_return': total_return,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'best_trade': self.reversion_df['net_return'].max(),
                'worst_trade': self.reversion_df['net_return'].min()
            }
            
            print(f"回归策略结果:")
            print(f"  总交易次数: {total_trades}")
            print(f"  胜率: {win_rate:.2%}")
            print(f"  平均收益: {avg_return:.4f}")
            print(f"  总收益: {total_return:.4f}")
            print(f"  夏普比率: {sharpe_ratio:.3f}")
            print(f"  最大回撤: {max_drawdown:.4f}")
        
        return self.reversion_df
    
    def _calculate_max_drawdown(self, cumulative_returns):
        """计算最大回撤"""
        if len(cumulative_returns) == 0:
            return 0.0
        
        running_max = cumulative_returns.expanding().max()
        drawdown = cumulative_returns - running_max
        return drawdown.min()
    
    def analyze_conditional_performance(self):
        """条件化性能分析"""
        print("\n=== 条件化性能分析 ===")
        
        if not hasattr(self, 'follow_df') or not hasattr(self, 'reversion_df'):
            print("请先运行策略分析")
            return
        
        # 按清算规模分析
        print("\n按清算规模分析:")
        for strategy_name, df in [('跟随策略', self.follow_df), ('回归策略', self.reversion_df)]:
            print(f"\n{strategy_name}:")
            
            # 小额清算 (100-1000)
            small_liq = df[(df['liquidation_quantity'] >= 100) & (df['liquidation_quantity'] < 1000)]
            if len(small_liq) > 0:
                print(f"  小额清算 (100-1000): 胜率 {(small_liq['net_return'] > 0).mean():.2%}, "
                      f"平均收益 {small_liq['net_return'].mean():.4f}")
            
            # 大额清算 (>=1000)
            large_liq = df[df['liquidation_quantity'] >= 1000]
            if len(large_liq) > 0:
                print(f"  大额清算 (>=1000): 胜率 {(large_liq['net_return'] > 0).mean():.2%}, "
                      f"平均收益 {large_liq['net_return'].mean():.4f}")
        
        # 按信号强度分析
        print("\n按信号强度分析:")
        for strategy_name, df in [('跟随策略', self.follow_df), ('回归策略', self.reversion_df)]:
            print(f"\n{strategy_name}:")
            
            # 强信号 (>0.8)
            strong_signal = df[df['signal_strength'] > 0.8]
            if len(strong_signal) > 0:
                print(f"  强信号 (>0.8): 胜率 {(strong_signal['net_return'] > 0).mean():.2%}, "
                      f"平均收益 {strong_signal['net_return'].mean():.4f}")
            
            # 弱信号 (<=0.8)
            weak_signal = df[df['signal_strength'] <= 0.8]
            if len(weak_signal) > 0:
                print(f"  弱信号 (<=0.8): 胜率 {(weak_signal['net_return'] > 0).mean():.2%}, "
                      f"平均收益 {weak_signal['net_return'].mean():.4f}")
    
    def generate_comparison_report(self):
        """生成比较报告"""
        print("\n" + "="*80)
        print(f"清算策略比较报告 - {self.symbol}")
        print("="*80)

        if 'follow_strategy' in self.results and 'reversion_strategy' in self.results:
            follow = self.results['follow_strategy']
            reversion = self.results['reversion_strategy']

            print(f"\n策略对比:")
            print(f"{'指标':<15} {'跟随策略':<15} {'回归策略':<15} {'优势策略':<15}")
            print("-" * 60)

            # 胜率比较
            better_wr = '跟随策略' if follow['win_rate'] > reversion['win_rate'] else '回归策略'
            print(f"{'胜率':<15} {follow['win_rate']:<15.2%} {reversion['win_rate']:<15.2%} {better_wr:<15}")

            # 平均收益比较
            better_ar = '跟随策略' if follow['avg_return'] > reversion['avg_return'] else '回归策略'
            print(f"{'平均收益':<15} {follow['avg_return']:<15.4f} {reversion['avg_return']:<15.4f} {better_ar:<15}")

            # 总收益比较
            better_tr = '跟随策略' if follow['total_return'] > reversion['total_return'] else '回归策略'
            print(f"{'总收益':<15} {follow['total_return']:<15.4f} {reversion['total_return']:<15.4f} {better_tr:<15}")

            # 夏普比率比较
            better_sr = '跟随策略' if follow['sharpe_ratio'] > reversion['sharpe_ratio'] else '回归策略'
            print(f"{'夏普比率':<15} {follow['sharpe_ratio']:<15.3f} {reversion['sharpe_ratio']:<15.3f} {better_sr:<15}")

            # 最大回撤比较（越小越好）
            better_dd = '跟随策略' if follow['max_drawdown'] > reversion['max_drawdown'] else '回归策略'
            print(f"{'最大回撤':<15} {follow['max_drawdown']:<15.4f} {reversion['max_drawdown']:<15.4f} {better_dd:<15}")

            print("\n结论:")
            # 综合评分
            follow_score = 0
            reversion_score = 0

            if follow['win_rate'] > reversion['win_rate']:
                follow_score += 1
            else:
                reversion_score += 1

            if follow['avg_return'] > reversion['avg_return']:
                follow_score += 1
            else:
                reversion_score += 1

            if follow['sharpe_ratio'] > reversion['sharpe_ratio']:
                follow_score += 1
            else:
                reversion_score += 1

            if follow['max_drawdown'] > reversion['max_drawdown']:  # 回撤越小越好
                reversion_score += 1
            else:
                follow_score += 1

            if follow_score > reversion_score:
                print("🏆 跟随策略在多数指标上表现更好")
                print("   建议：清算事件确实会造成Bybit跟随Binance的价格变动")
                print("   这表明市场存在明显的lead-lag效应，Binance作为leader")
            elif reversion_score > follow_score:
                print("🏆 回归策略在多数指标上表现更好")
                print("   建议：清算后的价格回归效应更强，适合反向交易")
                print("   这表明清算造成的价格偏离会快速修正")
            else:
                print("🤝 两种策略表现相当，需要进一步细化分析")

        return self.results

    def create_visualization(self):
        """创建可视化图表"""
        if not hasattr(self, 'follow_df') or not hasattr(self, 'reversion_df'):
            print("请先运行策略分析")
            return

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'{self.symbol} 清算策略比较分析', fontsize=16, fontweight='bold')

        # 1. 累积收益对比
        ax1 = axes[0, 0]
        follow_cumret = self.follow_df['net_return'].cumsum()
        reversion_cumret = self.reversion_df['net_return'].cumsum()

        ax1.plot(follow_cumret.index, follow_cumret.values, label='跟随策略', linewidth=2, color='blue')
        ax1.plot(reversion_cumret.index, reversion_cumret.values, label='回归策略', linewidth=2, color='red')
        ax1.set_title('累积收益对比')
        ax1.set_xlabel('交易序号')
        ax1.set_ylabel('累积收益')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 收益分布对比
        ax2 = axes[0, 1]
        ax2.hist(self.follow_df['net_return'], bins=20, alpha=0.7, label='跟随策略', color='blue')
        ax2.hist(self.reversion_df['net_return'], bins=20, alpha=0.7, label='回归策略', color='red')
        ax2.set_title('收益分布对比')
        ax2.set_xlabel('单次交易收益')
        ax2.set_ylabel('频次')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 按清算规模的表现
        ax3 = axes[1, 0]

        # 跟随策略按规模分组
        follow_small = self.follow_df[(self.follow_df['liquidation_quantity'] >= 100) &
                                     (self.follow_df['liquidation_quantity'] < 1000)]
        follow_large = self.follow_df[self.follow_df['liquidation_quantity'] >= 1000]

        # 回归策略按规模分组
        reversion_small = self.reversion_df[(self.reversion_df['liquidation_quantity'] >= 100) &
                                           (self.reversion_df['liquidation_quantity'] < 1000)]
        reversion_large = self.reversion_df[self.reversion_df['liquidation_quantity'] >= 1000]

        categories = ['小额清算\n(100-1000)', '大额清算\n(>=1000)']
        follow_returns = [follow_small['net_return'].mean() if len(follow_small) > 0 else 0,
                         follow_large['net_return'].mean() if len(follow_large) > 0 else 0]
        reversion_returns = [reversion_small['net_return'].mean() if len(reversion_small) > 0 else 0,
                            reversion_large['net_return'].mean() if len(reversion_large) > 0 else 0]

        x = np.arange(len(categories))
        width = 0.35

        ax3.bar(x - width/2, follow_returns, width, label='跟随策略', color='blue', alpha=0.7)
        ax3.bar(x + width/2, reversion_returns, width, label='回归策略', color='red', alpha=0.7)
        ax3.set_title('按清算规模的平均收益')
        ax3.set_xlabel('清算规模')
        ax3.set_ylabel('平均收益')
        ax3.set_xticks(x)
        ax3.set_xticklabels(categories)
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 胜率对比
        ax4 = axes[1, 1]
        strategies = ['跟随策略', '回归策略']
        win_rates = [self.results['follow_strategy']['win_rate'],
                    self.results['reversion_strategy']['win_rate']]
        colors = ['blue', 'red']

        bars = ax4.bar(strategies, win_rates, color=colors, alpha=0.7)
        ax4.set_title('胜率对比')
        ax4.set_ylabel('胜率')
        ax4.set_ylim(0, 1)

        # 添加数值标签
        for bar, rate in zip(bars, win_rates):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{rate:.1%}', ha='center', va='bottom', fontweight='bold')

        ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f'analysis_output/{self.symbol}_strategy_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()

        print(f"\n📊 可视化图表已保存到: analysis_output/{self.symbol}_strategy_comparison.png")

def main():
    """主函数"""
    # 分析WLDUSDT
    analyzer = LiquidationStrategyComparison('WLDUSDT')

    if not analyzer.load_analysis_data():
        print("无法加载数据，退出分析")
        return

    # 分析两种策略
    analyzer.analyze_follow_strategy()
    analyzer.analyze_reversion_strategy()

    # 条件化分析
    analyzer.analyze_conditional_performance()

    # 生成比较报告
    results = analyzer.generate_comparison_report()

    # 创建可视化
    analyzer.create_visualization()

    # 保存详细结果
    analyzer.save_detailed_results()

    return analyzer, results

def analyze_lead_lag_effectiveness():
    """分析lead-lag关系的有效性"""
    print("\n" + "="*80)
    print("Lead-Lag关系有效性分析")
    print("="*80)

    analyzer = LiquidationStrategyComparison('WLDUSDT')
    if not analyzer.load_analysis_data():
        return

    # 分析lag与收益的关系
    df = analyzer.enhanced_df

    print(f"\nLag分布分析:")
    lag_stats = df['optimal_lag'].describe()
    print(lag_stats)

    # 按lag分组分析
    print(f"\n按Lag分组的收益分析:")

    # Binance领先 (lag < 0)
    binance_lead = df[df['optimal_lag'] < 0]
    if len(binance_lead) > 0:
        avg_return_binance_lead = binance_lead['actual_return_bybit_10s'].mean()
        print(f"Binance领先 (lag < 0): {len(binance_lead)}个事件, 平均Bybit收益: {avg_return_binance_lead:.4f}")

    # 同步 (lag = 0)
    simultaneous = df[df['optimal_lag'] == 0]
    if len(simultaneous) > 0:
        avg_return_simultaneous = simultaneous['actual_return_bybit_10s'].mean()
        print(f"同步 (lag = 0): {len(simultaneous)}个事件, 平均Bybit收益: {avg_return_simultaneous:.4f}")

    # Bybit领先 (lag > 0)
    bybit_lead = df[df['optimal_lag'] > 0]
    if len(bybit_lead) > 0:
        avg_return_bybit_lead = bybit_lead['actual_return_bybit_10s'].mean()
        print(f"Bybit领先 (lag > 0): {len(bybit_lead)}个事件, 平均Bybit收益: {avg_return_bybit_lead:.4f}")

    # 相关性强度分析
    print(f"\n相关性强度分析:")
    high_corr = df[df['max_correlation'] > 0.8]
    low_corr = df[df['max_correlation'] <= 0.8]

    if len(high_corr) > 0:
        print(f"高相关性 (>0.8): {len(high_corr)}个事件, 平均Bybit收益: {high_corr['actual_return_bybit_10s'].mean():.4f}")
    if len(low_corr) > 0:
        print(f"低相关性 (<=0.8): {len(low_corr)}个事件, 平均Bybit收益: {low_corr['actual_return_bybit_10s'].mean():.4f}")

if __name__ == "__main__":
    analyzer, results = main()
    analyze_lead_lag_effectiveness()
