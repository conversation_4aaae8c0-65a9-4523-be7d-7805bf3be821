#!/usr/bin/env python3
"""
Lead-Lag清算跟随策略 V3.0 回测
基于深度分析的重构版本

主要改进:
1. 反向清算策略 - 清算预示反转
2. 多因子信号确认 - 提高信号质量  
3. 动态风险管理 - 自适应止损
4. Maker订单策略 - 降低手续费
"""

import pandas as pd
from pathlib import Path
from datetime import datetime, timezone

from nautilus_trader.backtest.engine import BacktestEngine
from nautilus_trader.backtest.models import FillModel
from nautilus_trader.config import BacktestEngineConfig
from nautilus_trader.model.currencies import USDT
from nautilus_trader.model.enums import AccountType, OmsType
from nautilus_trader.model.identifiers import Venue
from nautilus_trader.model.objects import Money

from nautilus_trader.test_kit.providers import TestInstrumentProvider

from lead_lag_liquidation_strategy_v3 import LeadLagLiquidationStrategyV3, LeadLagConfig


def run_lead_lag_v3_backtest():
    """运行Lead-Lag V3.0策略回测"""
    
    print("="*80)
    print("Lead-Lag清算跟随策略 V3.0 回测")
    print("="*80)
    print("🔧 V3.0核心改进:")
    print("   🔄 反向清算策略: 清算事件预示价格反转")
    print("   🔍 多因子确认: 清算量级 + 价格偏离 + 成交量")
    print("   🛡️ 动态风险管理: 自适应止损 + 回撤保护")
    print("   💰 Maker订单策略: 降低手续费成本")
    print("   🎯 目标: 胜率45-55%, 年化收益8-15%")
    print("="*80)
    
    # 检查数据文件
    data_dir = Path("./data")
    required_files = [
        "1000PEPEUSDT-PERP_BINANCE_trades_2025-08-01_2025-08-02.parquet",
        "1000PEPEUSDT-LINEAR_BYBIT_trades_2025-08-01_2025-08-02.parquet", 
        "1000PEPEUSDT-PERP_BINANCE_liquidations_2025-08-01_2025-08-02.parquet"
    ]
    
    for file in required_files:
        if not (data_dir / file).exists():
            print(f"❌ 缺少数据文件: {file}")
            return
            
    print("✅ 数据文件检查完成")
    
    # 交易对配置
    binance_instrument_id = TestInstrumentProvider.ethusdt_binance().id
    bybit_instrument_id = TestInstrumentProvider.ethusdt_binance().id
    
    # 修改为正确的交易对
    binance_instrument_id = binance_instrument_id._replace(symbol="1000PEPEUSDT-PERP")
    bybit_instrument_id = bybit_instrument_id._replace(
        symbol="1000PEPEUSDT-LINEAR", 
        venue=Venue("BYBIT")
    )
    
    print(f"🔍 检查交易对...")
    print(f"✅ 找到Binance交易对: {binance_instrument_id}")
    print(f"✅ 找到Bybit交易对: {bybit_instrument_id}")
    
    # V3.0策略配置 - 更保守和智能的参数
    strategy_config = LeadLagConfig(
        # 交易对配置
        binance_instrument_id=binance_instrument_id,
        bybit_instrument_id=bybit_instrument_id,

        # 🎯 V3.0核心参数 (基于深度分析优化)
        min_liquidation_value_usdt=50.0,      # 提高最小清算价值
        min_trade_value_usdt=10.0,            # 提高最小交易价值
        max_position_value_usdt=1000.0,       # 降低最大仓位
        signal_strength_threshold=0.02,       # 提高信号阈值

        # 🛡️ V3.0风险管理 (更严格的控制)
        stop_loss_pct=0.02,                   # 收紧止损到2%
        take_profit_pct=0.01,                 # 降低止盈到1%
        position_timeout_seconds=60,          # 缩短到1分钟
        max_drawdown_pct=0.03,                # 最大回撤3%

        # 🚀 V3.0执行优化
        ttl_ms=2000,                          # 2秒TTL
        ema_period=20,                        # 增加EMA周期

        # 🔧 V3.0新功能
        reverse_strategy=True,                # 启用反向策略
        multi_factor_confirmation=True,       # 多因子确认
        dynamic_risk_management=True,         # 动态风险管理
        maker_only=True,                      # 仅使用Maker订单
    )
    
    print("✅ V3.0策略配置:")
    print(f"   反向策略: {strategy_config.reverse_strategy}")
    print(f"   多因子确认: {strategy_config.multi_factor_confirmation}")
    print(f"   动态风险管理: {strategy_config.dynamic_risk_management}")
    print(f"   Maker订单: {strategy_config.maker_only}")
    print(f"   最小清算价值: ${strategy_config.min_liquidation_value_usdt}")
    print(f"   信号强度阈值: {strategy_config.signal_strength_threshold}")
    print(f"   止损: {strategy_config.stop_loss_pct*100}%")
    print(f"   止盈: {strategy_config.take_profit_pct*100}%")
    
    # 创建策略实例
    strategy = LeadLagLiquidationStrategyV3(strategy_config)
    
    print("\n🚀 初始化V3.0回测引擎...")
    
    # 回测引擎配置
    config = BacktestEngineConfig(
        trader_id="BACKTESTER-001",
        logging={"bypass_logging": False}
    )
    
    # 创建回测引擎
    engine = BacktestEngine(config=config)
    
    # 添加交易所
    engine.add_venue(
        venue=Venue("BINANCE"),
        oms_type=OmsType.NETTING,
        account_type=AccountType.MARGIN,
        base_currency=USDT,
        starting_balances=[Money(0, USDT)],  # Binance不交易，余额为0
    )
    
    engine.add_venue(
        venue=Venue("BYBIT"),
        oms_type=OmsType.NETTING,
        account_type=AccountType.MARGIN,
        base_currency=USDT,
        starting_balances=[Money(100_000, USDT)],  # 10万USDT起始资金
    )
    
    # 添加交易工具
    binance_instrument = TestInstrumentProvider.ethusdt_binance()
    binance_instrument = binance_instrument._replace(
        id=binance_instrument_id,
        symbol=binance_instrument_id.symbol,
        venue=binance_instrument_id.venue
    )
    
    bybit_instrument = TestInstrumentProvider.ethusdt_binance()
    bybit_instrument = bybit_instrument._replace(
        id=bybit_instrument_id,
        symbol=bybit_instrument_id.symbol,
        venue=bybit_instrument_id.venue
    )
    
    engine.add_instrument(binance_instrument)
    engine.add_instrument(bybit_instrument)
    
    # 添加策略
    engine.add_strategy(strategy)
    
    # 加载数据
    print("📊 加载交易数据...")
    
    # Binance交易数据
    binance_trades_file = data_dir / "1000PEPEUSDT-PERP_BINANCE_trades_2025-08-01_2025-08-02.parquet"
    engine.add_data(
        data=pd.read_parquet(binance_trades_file),
        data_cls="TradeTick",
        instrument_id=binance_instrument_id,
    )
    
    # Bybit交易数据  
    bybit_trades_file = data_dir / "1000PEPEUSDT-LINEAR_BYBIT_trades_2025-08-01_2025-08-02.parquet"
    engine.add_data(
        data=pd.read_parquet(bybit_trades_file),
        data_cls="TradeTick", 
        instrument_id=bybit_instrument_id,
    )
    
    # Binance清算数据
    liquidations_file = data_dir / "1000PEPEUSDT-PERP_BINANCE_liquidations_2025-08-01_2025-08-02.parquet"
    engine.add_data(
        data=pd.read_parquet(liquidations_file),
        data_cls="BinanceFuturesLiquidationOrder",
        instrument_id=binance_instrument_id,
    )
    
    print("⚡ 开始V3.0策略回测...")
    
    # 运行回测
    engine.run()
    
    print("\n" + "="*80)
    print("📊 V3.0回测结果分析")
    print("="*80)
    
    # 获取结果
    portfolio = engine.trader.portfolio
    account = portfolio.account(Venue("BYBIT"))
    
    if account:
        final_balance = account.balance_total(USDT)
        initial_balance = Money(100_000, USDT)
        total_pnl = final_balance - initial_balance
        
        print(f"💰 初始资金: {initial_balance}")
        print(f"💰 最终资金: {final_balance}")
        print(f"💰 总收益: {total_pnl}")
        print(f"📊 收益率: {(total_pnl.as_double()/initial_balance.as_double()*100):.2f}%")
    
    # 获取持仓统计
    positions = engine.trader.cache.positions()
    if positions:
        total_positions = len(positions)
        closed_positions = [p for p in positions if p.is_closed]
        profitable_positions = [p for p in closed_positions if p.realized_pnl.as_double() > 0]
        
        total_pnl = sum(p.realized_pnl.as_double() for p in closed_positions)
        win_rate = len(profitable_positions) / len(closed_positions) * 100 if closed_positions else 0
        
        print(f"\n📊 V3.0持仓统计:")
        print(f"   总持仓: {total_positions}")
        print(f"   已关闭: {len(closed_positions)}")
        print(f"   总PnL: {total_pnl:.2f} USDT")
        print(f"   胜率: {win_rate:.1f}%")
        print(f"   盈利仓位: {len(profitable_positions)}/{len(closed_positions)}")
    
    # 账户报告
    print(f"\n💰 V3.0账户报告:")
    for venue_str in ["BINANCE", "BYBIT"]:
        venue_obj = Venue(venue_str)
        account = portfolio.account(venue_obj)
        if account:
            print(f"   {venue_str} 账户报告:")
            print(f"     最终余额: {account.balance_total(USDT)}")
            if venue_str == "BYBIT":
                pnl = account.balance_total(USDT).as_double() - 100_000
                print(f"     净收益: {pnl:.2f} USDT")
    
    print(f"\n🎉 Lead-Lag V3.0策略回测完成!")
    print("💡 V3.0核心改进已实施:")
    print("💡 反向清算策略 - 清算预示反转")
    print("💡 多因子信号确认 - 提高信号质量")
    print("💡 动态风险管理 - 自适应风控")
    print("💡 Maker订单策略 - 降低交易成本")
    print("🎯 期望相比V2.0有显著改善!")


if __name__ == "__main__":
    run_lead_lag_v3_backtest()
