"""
LEAD-LAG清算跟随策略 (Lead-Lag Liquidation Follow Strategy)
"""

from decimal import Decimal
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from collections import deque

import numpy as np

from nautilus_trader.config import StrategyConfig
from nautilus_trader.core.data import Data
from nautilus_trader.model.data import TradeTick, DataType
from nautilus_trader.model.enums import OrderSide, OrderType, TimeInForce, PositionSide, AggressorSide
from nautilus_trader.model.identifiers import InstrumentId, ClientId
from nautilus_trader.model.instruments import Instrument
from nautilus_trader.model.orders import LimitOrder
from nautilus_trader.model.position import Position
from nautilus_trader.trading.strategy import Strategy
from nautilus_trader.model import Quantity, Price

# 导入指标
from nautilus_trader.indicators.average.ema import ExponentialMovingAverage

# 导入自定义清算数据类型
from nautilus_trader.adapters.binance.futures.types import BinanceFuturesLiquidationOrder


class LeadLagConfig(StrategyConfig, kw_only=True):
    """LEAD-LAG清算跟随策略配置 - 完全参考原始策略结构"""

    # 交易对配置
    binance_instrument_id: str  # Binance交易对 (指导市场)
    bybit_instrument_id: str    # Bybit交易对 (交易市场)

    # 简化的LEAD-LAG核心参数 - 反向策略优化
    min_liquidation_value_usdt: float = 100.0   # 最小清算价值 (提高门槛)
    min_trade_value_usdt: float = 5.0           # 最小交易资金(USDT)
    max_position_value_usdt: float = 500.0      # 最大仓位价值(USDT) (降低风险)
    signal_strength_threshold: float = 0.01     # 信号强度阈值 (降低门槛)

    # 简化参数
    ttl_ms: int = 100                          # 100ms订单生存时间
    ema_period: int = 20                       # EMA周期用于基差计算

    # 动态风险管理 - 基于市场状态的智能超时
    stop_loss_pct: float = 0.012               # 止损百分比 (1.2%)
    initial_take_profit_pct: float = 0.008     # 初始止盈百分比 (0.8%)
    trailing_stop_pct: float = 0.004           # 移动止损百分比 (0.4%)
    base_timeout_seconds: int = 300            # 基础超时时间 (5分钟)
    max_timeout_seconds: int = 600             # 最大超时时间 (10分钟)
    min_profit_for_trailing: float = 0.005     # 启动移动止损的最小盈利 (0.5%)
    volatility_timeout_multiplier: float = 2.0 # 波动率超时倍数


class LeadLagLiquidationStrategy(Strategy):
    """LEAD-LAG清算跟随策略实现 - 完全参考原始策略架构"""

    def __init__(self, config: LeadLagConfig) -> None:
        super().__init__(config)

        # 配置参数
        self.binance_instrument_id = InstrumentId.from_str(config.binance_instrument_id)
        self.bybit_instrument_id = InstrumentId.from_str(config.bybit_instrument_id)

        # 简化的数据存储
        self.binance_prices = deque(maxlen=100)
        self.bybit_prices = deque(maxlen=100)

        # 使用EMA计算基差均值
        self.basis_ema = ExponentialMovingAverage(config.ema_period)
        self.current_basis = 0.0

        # 清算事件存储
        self.recent_liquidations = deque(maxlen=10)

        # 策略状态
        self.binance_instrument: Optional[Instrument] = None
        self.bybit_instrument: Optional[Instrument] = None
        self.current_position: Optional[Position] = None
        self.position_entry_time: Optional[datetime] = None
        self.pending_orders: Dict[str, Any] = {}

        # 动态止盈止损跟踪
        self.position_peak_profit: Dict[str, float] = {}  # 跟踪每个仓位的最大盈利
        self.trailing_stop_active: Dict[str, bool] = {}   # 跟踪移动止损是否激活
        self.position_dynamic_timeout: Dict[str, int] = {} # 每个仓位的动态超时时间

        # 统计信息 
        self.total_signals = 0
        self.successful_trades = 0
        self.failed_trades = 0
        self.maker_fills = 0
        self.taker_fills = 0

    def on_start(self) -> None:
        """策略启动 - 完全参考原始策略"""
        self.log.info("🚀 启动LEAD-LAG清算跟随策略...")
        print("🚀 LEAD-LAG策略启动中...")

        # 获取交易对信息
        print("📊 检查交易对:")
        self.binance_instrument = self.cache.instrument(self.binance_instrument_id)
        self.bybit_instrument = self.cache.instrument(self.bybit_instrument_id)

        print(f"   Binance (指导): {self.binance_instrument_id} -> {'✅ 找到' if self.binance_instrument else '❌ 未找到'}")
        print(f"   Bybit (交易): {self.bybit_instrument_id} -> {'✅ 找到' if self.bybit_instrument else '❌ 未找到'}")

        # 验证交易对是否存在
        if not self.binance_instrument:
            self.log.info("无法找到Binance交易对: {self.binance_instrument_id}")
            self.stop()
            return

        if not self.bybit_instrument:
            self.log.info(f"无法找到Bybit交易对: {self.bybit_instrument_id}")
            self.stop()
            return

        print(f"📈 交易对详情:")
        print(f"   Binance: {self.binance_instrument.id} (价格精度: {self.binance_instrument.price_increment})")
        print(f"   Bybit: {self.bybit_instrument.id} (价格精度: {self.bybit_instrument.price_increment}, 数量精度: {self.bybit_instrument.size_increment})")

        # 订阅清算数据
        print("📡 订阅清算数据...")
        self.subscribe_data(
            data_type=DataType(
                BinanceFuturesLiquidationOrder,
                metadata={"instrument_id": self.binance_instrument_id}
            ),
            client_id=ClientId("BINANCE"),
        )

        # 订阅交易数据用于价格跟踪
        print("📡 订阅交易数据...")
        self.subscribe_trade_ticks(self.binance_instrument_id)
        self.subscribe_trade_ticks(self.bybit_instrument_id)


        print(f"✅ 简化LEAD-LAG策略配置:")
        print(f"   最小清算价值: ${self.config.min_liquidation_value_usdt}")
        print(f"   最小交易资金: ${self.config.min_trade_value_usdt}")
        print(f"   最大仓位价值: ${self.config.max_position_value_usdt}")
        print(f"   信号强度阈值: {self.config.signal_strength_threshold}")
        print(f"   TTL: {self.config.ttl_ms}ms")
        print(f"   EMA周期: {self.config.ema_period}")
        print(f"   基础超时: {self.config.base_timeout_seconds}s")
        print(f"   最大超时: {self.config.max_timeout_seconds}s")

        self.log.info(f"简化LEAD-LAG策略已启动 - 监听 {self.binance_instrument_id} 清算事件")
        self.log.info(f"交易执行在 {self.bybit_instrument_id} (使用简化清算跟随逻辑)")
        print("🎯 简化LEAD-LAG策略已启动，等待清算事件...")

        # 添加事件计数器
        self.event_count = 0
        self.data_event_count = 0

    def on_trade_tick(self, tick: TradeTick) -> None:
        """处理交易数据 - 价格跟踪和基差计算"""
        if tick.instrument_id == self.binance_instrument_id:
            # Binance价格数据
            price = tick.price.as_double()
            volume = tick.size.as_double()

            self.binance_prices.append(price)

        elif tick.instrument_id == self.bybit_instrument_id:
            # Bybit价格数据
            price = tick.price.as_double()
            volume = tick.size.as_double()

            self.bybit_prices.append(price)

            # 计算基差 (当两个市场都有数据时)
            if self.binance_prices and self.bybit_prices:
                self._update_basis()

            # 仓位管理检查 (GTD订单由nautilus_trader自动处理)
            self.check_position_timeout()

    def _update_basis(self) -> None:
        """使用EMA计算基差均值 - 简化版本"""
        if not self.binance_prices or not self.bybit_prices:
            return

        binance_price = self.binance_prices[-1]
        bybit_price = self.bybit_prices[-1]

        # 计算基差 (Binance - Bybit)
        basis = binance_price - bybit_price

        # 使用EMA更新基差均值
        self.basis_ema.update_raw(basis)
        if self.basis_ema.initialized:
            self.current_basis = self.basis_ema.value

    def on_data(self, data: Data) -> None:
        """处理自定义数据 - 清算事件"""
        # 增加事件计数器
        self.data_event_count += 1

        print(f"📥 收到数据 #{self.data_event_count}: {type(data).__name__}")
        self.log.info(f"收到数据 #{self.data_event_count}: {type(data).__name__}")

        if isinstance(data, BinanceFuturesLiquidationOrder):
            print(f"✅ 确认收到清算数据: {data.instrument_id}")
            print(f"   清算方向: {data.order_side}")
            print(f"   清算数量: {data.original_quantity}")
            print(f"   清算价格: {data.price}")
            self.on_liquidation_event(data)

    def on_liquidation_event(self, liquidation: BinanceFuturesLiquidationOrder) -> None:
        """处理清算事件 - LEAD-LAG核心逻辑"""
        # 修复数据访问方式，参考原始策略
        liquidation_value = liquidation.price.as_double() * liquidation.original_quantity.as_double()

        print(f"💥 收到清算事件: {liquidation.order_side} {liquidation.original_quantity} @ {liquidation.price}")
        print(f"💰 清算价值: ${liquidation_value:.2f}")
        print(f"🔍 交易对: {liquidation.instrument_id}")

        # 检查是否是我们关注的交易对
        if liquidation.instrument_id != self.binance_instrument_id:
            print(f"⚠️  交易对不匹配: {liquidation.instrument_id} != {self.binance_instrument_id}")
            return

        # 存储清算事件
        self.recent_liquidations.append({
            'timestamp': liquidation.ts_event,
            'side': liquidation.order_side,
            'quantity': liquidation.original_quantity.as_double(),
            'price': liquidation.price.as_double(),
            'value': liquidation_value
        })

        # 检查是否应该跟随这个清算事件
        if self.should_trade_liquidation(liquidation):
            self.total_signals += 1
            signal = self.generate_trade_signal(liquidation)
            if signal:
                self.execute_trade_signal(signal)

    def should_trade_liquidation(self, liquidation: BinanceFuturesLiquidationOrder) -> bool:
        """判断是否应该跟随清算事件"""
        # 1. 清算价值过滤
        liquidation_value = liquidation.price.as_double() * liquidation.original_quantity.as_double()
        if liquidation_value < self.config.min_liquidation_value_usdt:
            print(f"❌ 清算价值太小: ${liquidation_value:.2f} < ${self.config.min_liquidation_value_usdt}")
            return False

        # 2. 检查当前仓位价值
        open_positions = self.cache.positions_open(
            instrument_id=self.bybit_instrument_id,
            strategy_id=self.id
        )
        if open_positions and self.bybit_prices:
            current_price = self.bybit_prices[-1]
            total_value = sum(abs(pos.quantity.as_double()) * current_price for pos in open_positions)
            if total_value >= self.config.max_position_value_usdt:
                print(f"❌ 仓位价值已满: ${total_value:.2f}")
                return False

        # 3. 检查价格数据可用性
        if not self.binance_prices or not self.bybit_prices:
            print(f"❌ 价格数据不足")
            return False

        # 4. 计算信号强度
        signal_strength = self.calculate_signal_strength(liquidation)
        if signal_strength < self.config.signal_strength_threshold:
            print(f"❌ 信号强度不足: {signal_strength:.4f} < {self.config.signal_strength_threshold}")
            return False

        print(f"✅ 清算事件通过过滤，信号强度: {signal_strength:.4f}")
        return True

    def calculate_signal_strength(self, liquidation: BinanceFuturesLiquidationOrder) -> float:
        """简化的信号强度计算"""
        liquidation_value = liquidation.price.as_double() * liquidation.original_quantity.as_double()

        # 基于清算价值的简单信号强度
        return min(liquidation_value / 10000.0, 1.0)  # 归一化到0-1



    def generate_trade_signal(self, liquidation: BinanceFuturesLiquidationOrder) -> Optional[Dict[str, Any]]:
        """生成交易信号 - 反向清算策略（基于测试结果优化）"""
        if not self.bybit_prices:
            return None

        # 获取当前市场价格
        current_bybit_price = self.bybit_prices[-1]
        liquidation_price = liquidation.price.as_double()
        liquidation_value = liquidation_price * liquidation.original_quantity.as_double()

        # 反向交易逻辑（基于56%胜率的测试结果）：
        # 多单被清算(SELL) -> 预期反弹 -> 做多
        # 空单被清算(BUY) -> 预期回调 -> 做空
        if liquidation.order_side == OrderSide.SELL:
            # 多单被清算，预期反弹，做多
            trade_side = OrderSide.BUY
            # 使用限价单获得更好的成交价格
            order_price = current_bybit_price * 0.9995  # 0.05%的价差，更保守
        else:
            # 空单被清算，预期回调，做空
            trade_side = OrderSide.SELL
            # 使用限价单获得更好的成交价格
            order_price = current_bybit_price * 1.0005  # 0.05%的价差，更保守

        # 计算交易数量
        quantity = self.calculate_trade_quantity(liquidation_value, current_bybit_price)

        signal = {
            'side': trade_side,
            'quantity': quantity,
            'order_price': order_price,
            'liquidation_price': liquidation_price,
            'liquidation_value': liquidation_value,
            'timestamp': self.clock.timestamp_ns()
        }

        print(f"🎯 简化交易信号: {signal['side']} {signal['quantity']:.0f} @ {signal['order_price']:.6f}")
        print(f"   清算价格: {liquidation_price:.6f}, 清算价值: ${liquidation_value:.0f}")

        return signal

    def calculate_trade_quantity(self, liquidation_value: float, current_price: float) -> float:
        """计算交易数量 - 反向策略优化的仓位管理"""
        if not self.bybit_instrument or current_price <= 0:
            return 0.0

        # 更保守的仓位大小：基于清算价值的5%而不是10%
        target_value = min(liquidation_value * 0.05, self.config.max_position_value_usdt)

        # 确保最小交易资金
        target_value = max(target_value, self.config.min_trade_value_usdt)

        # 根据当前价格计算数量
        raw_quantity = target_value / current_price

        # 使用instrument.make_qty确保精度正确
        return self.bybit_instrument.make_qty(raw_quantity).as_double()

    def execute_trade_signal(self, signal: Dict[str, Any]) -> None:
        """执行交易信号 - 使用限价单优化成交质量"""
        try:
            # 检查账户余额
            account = self.cache.account_for_venue(self.bybit_instrument.id.venue)
            if not account:
                print(f"❌ 无法获取账户信息")
                self.failed_trades += 1
                return

            # 计算订单参数 - 使用instrument API确保正确精度
            side = signal['side']
            quantity = self.bybit_instrument.make_qty(signal['quantity'])
            price = self.bybit_instrument.make_price(signal['order_price'])

            # 创建限价单以获得更好的成交价格和手续费率
            order = self.order_factory.limit(
                instrument_id=self.bybit_instrument_id,
                order_side=side,
                quantity=quantity,
                price=price,
                time_in_force=TimeInForce.GTC,  # 使用GTC而不是GTD
                post_only=False,  # 允许立即成交
            )

            # 提交订单
            self.submit_order(order)

            # 记录订单
            self.pending_orders[order.client_order_id] = {
                'signal': signal,
                'submit_time': self.clock.timestamp_ns(),
                'order': order
            }

            print(f"📤 提交限价单: {order.client_order_id} - {side} {quantity} @ {price}")
            self.log.info(f"提交LEAD-LAG限价单: {order.client_order_id}")

        except Exception as e:
            print(f"❌ 订单提交失败: {e}")
            self.log.error(f"订单提交失败: {e}")
            self.failed_trades += 1

    def on_stop(self) -> None:
        """策略停止时的清理"""
        self.log.info("停止LEAD-LAG清算跟随策略...")
        print("🛑 LEAD-LAG策略停止")

        # 打印最终统计
        print(f"\n📊 最终性能统计:")
        print(f"   total_signals: {self.total_signals}")
        print(f"   successful_trades: {self.successful_trades}")
        print(f"   failed_trades: {self.failed_trades}")
        print(f"   success_rate: {self.successful_trades / max(1, self.total_signals):.4f}")
        print(f"   maker_fills: {self.maker_fills}")
        print(f"   taker_fills: {self.taker_fills}")
        print(f"   current_basis: {self.current_basis:.4f}")

        # 使用内部API取消所有挂单
        self.cancel_all_orders(self.bybit_instrument_id)

        # 使用内部API关闭所有仓位
        self.close_all_positions(self.bybit_instrument_id)

        # 清理本地状态
        self.pending_orders.clear()
        self.recent_liquidations.clear()

        print(f"\n🎯 简化LEAD-LAG策略总结:")
        print(f"   📊 Maker成交率: {self.maker_fills / max(1, self.maker_fills + self.taker_fills) * 100:.1f}%")
        print(f"   🎲 交易成功率: {self.successful_trades / max(1, self.total_signals) * 100:.1f}%")
        print(f"   📈 当前基差: {self.current_basis:.6f}")
        print(f"   📈 EMA基差值: {self.basis_ema.value if self.basis_ema.initialized else 0:.6f}")

        self.log.info("LEAD-LAG清算跟随策略已停止")

    def on_order_denied(self, event) -> None:
        """处理订单被拒绝事件"""
        print(f"❌ 订单被拒绝: {event.client_order_id} - {event.reason}")
        self.log.warning(f"订单被拒绝: {event.client_order_id} - {event.reason}")

        # 从待处理订单中移除
        if event.client_order_id in self.pending_orders:
            del self.pending_orders[event.client_order_id]

        self.failed_trades += 1

    def on_order_rejected(self, event) -> None:
        """处理订单被拒绝事件"""
        print(f"❌ 订单被拒绝: {event.client_order_id} - {event.reason}")
        self.log.warning(f"订单被拒绝: {event.client_order_id} - {event.reason}")

        # 从待处理订单中移除
        if event.client_order_id in self.pending_orders:
            del self.pending_orders[event.client_order_id]

        self.failed_trades += 1

    def on_order_accepted(self, event) -> None:
        """处理订单被接受事件"""
        print(f"✅ 订单被接受: {event.client_order_id}")
        self.log.info(f"订单被接受: {event.client_order_id}")

    def on_order_filled(self, event) -> None:
        """处理订单成交事件"""
        print(f"🎉 订单成交: {event.client_order_id} - {event.last_qty} @ {event.last_px}")
        self.log.info(f"订单成交: {event.client_order_id} - {event.last_qty} @ {event.last_px}")

        # 判断是Maker还是Taker
        if hasattr(event, 'liquidity_side'):
            if event.liquidity_side.name == 'MAKER':
                self.maker_fills += 1
                print(f"📊 Maker成交: {event.client_order_id}")
            else:
                self.taker_fills += 1
                print(f"📊 Taker成交: {event.client_order_id}")

        # 从待处理订单中移除
        if event.client_order_id in self.pending_orders:
            del self.pending_orders[event.client_order_id]

        self.successful_trades += 1


    def check_position_timeout(self) -> None:
        """检查仓位超时和止盈止损 - 完全参考原始策略实现"""
        # 使用缓存API直接获取开仓位
        open_positions = self.cache.positions_open(
            instrument_id=self.bybit_instrument_id,
            strategy_id=self.id
        )

        if not open_positions:
            return

        position = open_positions[0]  # NETTING模式下每个交易对只有一个仓位

        # 检查止盈止损
        if self._check_take_profit_stop_loss(position):
            return  # 如果已经平仓，直接返回

        # 使用仓位的开仓时间而不是本地记录的时间
        current_time_ns = self.clock.timestamp_ns()
        elapsed_ns = current_time_ns - position.ts_opened
        elapsed_seconds = elapsed_ns / 1_000_000_000  # 转换为秒

        # 计算动态超时时间
        dynamic_timeout = self._calculate_dynamic_timeout(position)

        if elapsed_seconds > dynamic_timeout:
            position_id = position.id.value
            self.log.info(f"动态超时平仓 ({elapsed_seconds:.1f}s >= {dynamic_timeout}s)")
            print(f"⏰ 动态超时平仓: {elapsed_seconds:.1f}s >= {dynamic_timeout}s")
            self._cleanup_position_tracking(position_id)
            self.close_position_custom(position)

    def _check_take_profit_stop_loss(self, position: Position) -> bool:
        """动态止盈止损 - 基于趋势跟踪的智能退出"""
        try:
            if not self.bybit_prices:
                return False

            current_price = self.bybit_prices[-1]
            position_id = position.id.value

            # 安全地获取入场价格
            if hasattr(position.avg_px_open, 'as_double'):
                entry_price = position.avg_px_open.as_double()
            else:
                entry_price = float(position.avg_px_open)

            # 计算当前盈亏百分比
            if position.side == PositionSide.LONG:
                price_change_pct = (current_price - entry_price) / entry_price
            else:  # SHORT
                price_change_pct = (entry_price - current_price) / entry_price

            # 初始化跟踪变量
            if position_id not in self.position_peak_profit:
                self.position_peak_profit[position_id] = price_change_pct
                self.trailing_stop_active[position_id] = False

            # 更新最大盈利
            if price_change_pct > self.position_peak_profit[position_id]:
                self.position_peak_profit[position_id] = price_change_pct

            # 检查是否启动移动止损
            if (not self.trailing_stop_active[position_id] and
                price_change_pct >= self.config.min_profit_for_trailing):
                self.trailing_stop_active[position_id] = True
                print(f"🎯 启动移动止损: 当前盈利 {price_change_pct:.4f}")

            # 移动止损逻辑
            if self.trailing_stop_active[position_id]:
                # 从最高盈利点回撤超过移动止损阈值
                drawdown_from_peak = self.position_peak_profit[position_id] - price_change_pct
                if drawdown_from_peak >= self.config.trailing_stop_pct:
                    self.log.info(f"触发移动止损: 从峰值{self.position_peak_profit[position_id]:.4f}回撤{drawdown_from_peak:.4f}")
                    print(f"📈 移动止损: 峰值{self.position_peak_profit[position_id]:.4f} -> 当前{price_change_pct:.4f}")
                    self._cleanup_position_tracking(position_id)
                    self.close_position_custom(position)
                    return True

            # 初始止盈 (只在未启动移动止损时使用)
            if (not self.trailing_stop_active[position_id] and
                price_change_pct >= self.config.initial_take_profit_pct):
                self.log.info(f"触发初始止盈: {price_change_pct:.4f} >= {self.config.initial_take_profit_pct}")
                print(f"💰 初始止盈: {price_change_pct:.4f}")
                self._cleanup_position_tracking(position_id)
                self.close_position_custom(position)
                return True

            # 固定止损
            if price_change_pct <= -self.config.stop_loss_pct:
                self.log.info(f"触发止损: {price_change_pct:.4f} <= {-self.config.stop_loss_pct}")
                print(f"🛑 止损: {price_change_pct:.4f}")
                self._cleanup_position_tracking(position_id)
                self.close_position_custom(position)
                return True

            return False

        except Exception as e:
            self.log.error(f"检查止盈止损时出错: {e}")
            return False

    def _cleanup_position_tracking(self, position_id: str):
        """清理仓位跟踪数据"""
        if position_id in self.position_peak_profit:
            del self.position_peak_profit[position_id]
        if position_id in self.trailing_stop_active:
            del self.trailing_stop_active[position_id]
        if position_id in self.position_dynamic_timeout:
            del self.position_dynamic_timeout[position_id]

    def _calculate_dynamic_timeout(self, position: Position) -> int:
        """计算动态超时时间 - 基于仓位盈亏和市场状态"""
        position_id = position.id.value

        # 如果已经计算过，直接返回
        if position_id in self.position_dynamic_timeout:
            return self.position_dynamic_timeout[position_id]

        base_timeout = self.config.base_timeout_seconds

        # 计算当前盈亏
        if self.bybit_prices:
            current_price = self.bybit_prices[-1]
            if hasattr(position.avg_px_open, 'as_double'):
                entry_price = position.avg_px_open.as_double()
            else:
                entry_price = float(position.avg_px_open)

            if position.side == PositionSide.LONG:
                price_change_pct = (current_price - entry_price) / entry_price
            else:
                price_change_pct = (entry_price - current_price) / entry_price

            # 动态调整超时时间
            if price_change_pct > 0:
                # 盈利时延长超时时间，让利润奔跑
                profit_multiplier = 1 + (price_change_pct * 20)  # 每1%盈利延长20%时间
                dynamic_timeout = int(base_timeout * profit_multiplier)
                print(f"📈 盈利{price_change_pct:.3f}，延长超时至{dynamic_timeout}s")
            elif price_change_pct < -0.005:  # 亏损超过0.5%时缩短超时
                # 亏损时缩短超时时间，快速止损
                loss_multiplier = max(0.3, 1 + (price_change_pct * 10))  # 每1%亏损缩短10%时间
                dynamic_timeout = int(base_timeout * loss_multiplier)
                print(f"📉 亏损{price_change_pct:.3f}，缩短超时至{dynamic_timeout}s")
            else:
                # 小幅波动时使用基础超时
                dynamic_timeout = base_timeout
        else:
            dynamic_timeout = base_timeout

        # 限制在合理范围内
        dynamic_timeout = max(60, min(dynamic_timeout, self.config.max_timeout_seconds))

        # 缓存结果
        self.position_dynamic_timeout[position_id] = dynamic_timeout

        return dynamic_timeout


    def close_position_custom(self, position: Position) -> None:
        """使用nautilus_trader内部API平仓"""
        try:
            if position.is_closed:
                return

            # 使用nautilus_trader内部API直接平仓
            self.close_all_positions(self.bybit_instrument_id)
            print(f"📤 使用内部API平仓: {position.side} {position.quantity}")
            self.log.info(f"使用内部API平仓: {position.side} {position.quantity}")

        except Exception as e:
            print(f"❌ 平仓时出错: {e}")
            self.log.error(f"平仓时出错: {e}")