"""
清算跟随策略 (Liquidation Follow Strategy)
基于Binance清算事件在Bybit交易所进行跟随交易的策略

"""

from decimal import Decimal
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from collections import deque

import numpy as np

from nautilus_trader.config import StrategyConfig
from nautilus_trader.core.data import Data
from nautilus_trader.model.data import TradeTick, DataType
from nautilus_trader.model.enums import OrderSide, OrderType, TimeInForce, PositionSide, AggressorSide
from nautilus_trader.model.identifiers import InstrumentId, ClientId
from nautilus_trader.model.instruments import Instrument
from nautilus_trader.model.orders import LimitOrder
from nautilus_trader.model.position import Position
from nautilus_trader.trading.strategy import Strategy
from nautilus_trader.model import Quantity, Price

# 导入自定义清算数据类型
from nautilus_trader.adapters.binance.futures.types import BinanceFuturesLiquidationOrder


class LiquidationFollowConfig(StrategyConfig, kw_only=True):
    """清算跟随策略配置 """

    # 交易对配置
    binance_instrument_id: str  # Binance交易对 (用于监听清算)
    bybit_instrument_id: str    # Bybit交易对 (用于执行交易)

    # 策略参数 - 基于回测结果优化
    min_liquidation_value_usdt: float = 10.0    # 降低最小清算价值，增加交易机会
    max_trade_size: Decimal = Decimal("1000")   # 最大交易数量
    signal_strength_threshold: float = 0.1      # 大幅降低信号强度阈值
    correlation_threshold: float = 0.05         # 大幅降低相关性阈值

    # Maker订单配置 - 优化订单执行
    maker_spread_bps: int = 5                   # 保持较小价差以提高成交率
    order_timeout_seconds: int = 30             # 延长订单超时时间
    max_order_retries: int = 2                  # 减少重试次数，避免过度交易

    # 风险管理 - 优化风险控制
    max_position_size: Decimal = Decimal("5000")  # 最大仓位
    stop_loss_pct: float = 0.02                   # 止损百分比 (2%)
    take_profit_pct: float = 0.01                 # 止盈百分比 (1%)
    position_timeout_seconds: int = 120           # 延长仓位超时到2分钟

    # 数据窗口 - 优化数据分析
    price_history_window: int = 60  # 价格历史窗口 (秒)
    correlation_window: int = 10    # 缩短相关性计算窗口，提高敏感度


class LiquidationFollowStrategy(Strategy):
    """清算跟随策略实现 """

    def __init__(self, config: LiquidationFollowConfig) -> None:
        super().__init__(config)

        # 配置参数
        self.binance_instrument_id = InstrumentId.from_str(config.binance_instrument_id)
        self.bybit_instrument_id = InstrumentId.from_str(config.bybit_instrument_id)

        # 数据存储
        self.binance_prices = deque(maxlen=config.price_history_window)
        self.bybit_prices = deque(maxlen=config.price_history_window)
        self.price_timestamps = deque(maxlen=config.price_history_window)

        # 清算事件存储
        self.recent_liquidations = deque(maxlen=10)

        # 策略状态
        self.binance_instrument: Optional[Instrument] = None
        self.bybit_instrument: Optional[Instrument] = None
        self.current_position: Optional[Position] = None
        self.position_entry_time: Optional[datetime] = None
        self.pending_orders: Dict[str, Any] = {}

        # 统计信息
        self.total_signals = 0
        self.successful_trades = 0
        self.failed_trades = 0
        self.maker_fills = 0
        self.taker_fills = 0

    def on_start(self) -> None:
        """策略启动时的初始化 - 使用nautilus_trader内部API优化"""
        self.log.info("🚀 启动清算跟随策略 ...")
        print("🚀 策略启动中...")

        # 使用缓存API获取交易对信息
        self.binance_instrument = self.cache.instrument(self.binance_instrument_id)
        self.bybit_instrument = self.cache.instrument(self.bybit_instrument_id)

        print(f"📊 检查交易对:")
        print(f"   Binance: {self.binance_instrument_id} -> {'✅ 找到' if self.binance_instrument else '❌ 未找到'}")
        print(f"   Bybit: {self.bybit_instrument_id} -> {'✅ 找到' if self.bybit_instrument else '❌ 未找到'}")

        # 验证交易对是否存在
        if not self.binance_instrument:
            error_msg = f"无法找到Binance交易对: {self.binance_instrument_id}"
            self.log.error(error_msg)
            print(f"❌ 错误: {error_msg}")
            # 尝试从所有可用交易对中查找类似的
            available_instruments = self.cache.instruments()
            binance_instruments = [inst for inst in available_instruments if 'BINANCE' in str(inst.id)]
            if binance_instruments:
                print(f"💡 可用的Binance交易对: {[str(inst.id) for inst in binance_instruments[:5]]}")
            self.stop()
            return

        if not self.bybit_instrument:
            error_msg = f"无法找到Bybit交易对: {self.bybit_instrument_id}"
            self.log.error(error_msg)
            print(f"❌ 错误: {error_msg}")
            # 尝试从所有可用交易对中查找类似的
            available_instruments = self.cache.instruments()
            bybit_instruments = [inst for inst in available_instruments if 'BYBIT' in str(inst.id)]
            if bybit_instruments:
                print(f"💡 可用的Bybit交易对: {[str(inst.id) for inst in bybit_instruments[:5]]}")
            self.stop()
            return

        print(f"📈 交易对详情:")
        print(f"   Binance: {self.binance_instrument.id} (价格精度: {self.binance_instrument.price_increment})")
        print(f"   Bybit: {self.bybit_instrument.id} (价格精度: {self.bybit_instrument.price_increment}, 数量精度: {self.bybit_instrument.size_increment})")

        # 订阅清算数据
        print("📡 订阅清算数据...")
        self.subscribe_data(
            data_type=DataType(
                BinanceFuturesLiquidationOrder,
                metadata={"instrument_id": self.binance_instrument_id}
            ),
            client_id=ClientId("BINANCE"),
        )

        # 订阅交易数据用于价格跟踪
        print("📡 订阅交易数据...")
        self.subscribe_trade_ticks(self.binance_instrument_id)
        self.subscribe_trade_ticks(self.bybit_instrument_id)

        # 验证配置参数
        self._validate_config()

        print(f"✅ 策略配置:")
        print(f"   最小清算价值: ${self.config.min_liquidation_value_usdt}")
        print(f"   信号强度阈值: {self.config.signal_strength_threshold}")
        print(f"   相关性阈值: {self.config.correlation_threshold}")
        print(f"   相关性窗口: {self.config.correlation_window}")
        print(f"   Maker价差: {self.config.maker_spread_bps} bps")
        print(f"   订单超时: {self.config.order_timeout_seconds}s")
        print(f"   仓位超时: {self.config.position_timeout_seconds}s")
        print(f"   最大重试次数: {self.config.max_order_retries}")

        self.log.info(f"策略已启动 - 监听 {self.binance_instrument_id} 清算事件")
        self.log.info(f"交易执行在 {self.bybit_instrument_id} (使用Maker订单)")
        print("🎯 策略已启动，等待清算事件...")

        # 添加事件计数器
        self.event_count = 0

    def _validate_config(self) -> None:
        """验证策略配置参数"""
        if self.config.min_liquidation_value_usdt <= 0:
            raise ValueError("min_liquidation_value_usdt must be positive")

        if not (0 < self.config.signal_strength_threshold <= 1):
            raise ValueError("signal_strength_threshold must be between 0 and 1")

        if not (0 < self.config.correlation_threshold <= 1):
            raise ValueError("correlation_threshold must be between 0 and 1")

        if self.config.maker_spread_bps <= 0:
            raise ValueError("maker_spread_bps must be positive")

        if self.config.order_timeout_seconds <= 0:
            raise ValueError("order_timeout_seconds must be positive")

        if self.config.position_timeout_seconds <= 0:
            raise ValueError("position_timeout_seconds must be positive")

        print("✅ 配置参数验证通过")

    def on_stop(self) -> None:
        """策略停止时的清理 - 使用nautilus_trader内部API优化"""
        self.log.info("停止清算跟随策略...")
        print("🛑 停止清算跟随策略...")

        # 使用内部API取消所有挂单
        self.cancel_all_orders(self.bybit_instrument_id)

        # 使用内部API关闭所有仓位
        self.close_all_positions(self.bybit_instrument_id)

        # 清理本地状态
        self.pending_orders.clear()
        self.current_position = None
        self.position_entry_time = None

        # 打印统计信息
        total_trades = self.successful_trades + self.failed_trades
        success_rate = (self.successful_trades / total_trades * 100) if total_trades > 0 else 0
        maker_rate = (self.maker_fills / (self.maker_fills + self.taker_fills) * 100) if (self.maker_fills + self.taker_fills) > 0 else 0

        print(f"📊 策略统计:")
        print(f"  总信号数: {self.total_signals}")
        print(f"  总交易数: {total_trades}")
        print(f"  成功交易: {self.successful_trades}")
        print(f"  失败交易: {self.failed_trades}")
        print(f"  成功率: {success_rate:.2f}%")
        print(f"  Maker成交率: {maker_rate:.2f}%")

        self.log.info(f"策略统计:")
        self.log.info(f"  总信号数: {self.total_signals}")
        self.log.info(f"  总交易数: {total_trades}")
        self.log.info(f"  成功交易: {self.successful_trades}")
        self.log.info(f"  失败交易: {self.failed_trades}")
        self.log.info(f"  成功率: {success_rate:.2f}%")
        self.log.info(f"  Maker成交率: {maker_rate:.2f}%")

    def on_data(self, data: Data) -> None:
        """处理自定义数据 - 主要是清算事件"""
        self.event_count += 1
        print(f"📥 收到数据 #{self.event_count}: {type(data).__name__}")
        self.log.info(f"收到数据 #{self.event_count}: {type(data).__name__}")

        if isinstance(data, BinanceFuturesLiquidationOrder):
            print(f"💥 收到清算事件: {data.instrument_id} {data.order_side} {data.original_quantity} @ {data.price}")
            self.log.info(f"收到清算事件: {data}")
            self.on_liquidation_event(data)
        else:
            print(f"⚠️  未知数据类型: {type(data)}")
            self.log.warning(f"未知数据类型: {type(data)}")

    def on_event(self, event) -> None:
        """捕获所有事件的通用处理器"""
        print(f"🎪 收到事件: {type(event).__name__}")
        super().on_event(event)

    def on_liquidation_event(self, liquidation: BinanceFuturesLiquidationOrder) -> None:
        """处理Binance清算事件"""
        print(f"💥 处理清算事件: {liquidation.instrument_id}")

        # 检查是否是我们关注的交易对
        if liquidation.instrument_id != self.binance_instrument_id:
            print(f"⚠️  交易对不匹配: {liquidation.instrument_id} != {self.binance_instrument_id}")
            return

        # 计算清算价值 (price * original_quantity)
        liquidation_value = liquidation.price.as_double() * liquidation.original_quantity.as_double()
        print(f"💰 清算价值: ${liquidation_value:.2f} (阈值: ${self.config.min_liquidation_value_usdt})")

        # 检查清算价值是否达到阈值
        if liquidation_value < self.config.min_liquidation_value_usdt:
            print(f"❌ 清算价值太小，忽略: ${liquidation_value:.2f} < ${self.config.min_liquidation_value_usdt}")
            self.log.debug(f"清算价值太小，忽略: ${liquidation_value:.2f}")
            return

        print(f"✅ 清算事件符合条件: {liquidation.order_side} {liquidation.original_quantity} @ {liquidation.price} (价值: ${liquidation_value:.2f})")
        self.log.info(f"检测到清算事件: {liquidation.order_side} {liquidation.original_quantity} @ {liquidation.price} (价值: ${liquidation_value:.2f})")

        # 存储清算事件
        self.recent_liquidations.append({
            'timestamp': self.clock.timestamp_ns(),
            'side': liquidation.order_side,
            'quantity': liquidation.original_quantity.as_double(),
            'price': liquidation.price.as_double(),
            'value': liquidation_value
        })

        print(f"📊 最近清算事件数量: {len(self.recent_liquidations)}")

        # 分析并生成交易信号
        print("🔍 开始分析交易信号...")
        self.analyze_and_trade(liquidation)

    def on_trade_tick(self, tick: TradeTick) -> None:
        """处理交易tick数据，用于价格跟踪"""
        current_time = self.clock.timestamp_ns()

        if tick.instrument_id == self.binance_instrument_id:
            self.binance_prices.append(tick.price.as_double())
            self.price_timestamps.append(current_time)
            # if len(self.binance_prices) % 10 == 0:  # 每10个tick打印一次
            #     print(f"📈 Binance价格更新: {tick.price} (数据点: {len(self.binance_prices)})")
        elif tick.instrument_id == self.bybit_instrument_id:
            self.bybit_prices.append(tick.price.as_double())
            # if len(self.bybit_prices) % 10 == 0:  # 每10个tick打印一次
            #     print(f"📈 Bybit价格更新: {tick.price} (数据点: {len(self.bybit_prices)})")

        # 检查仓位和订单超时
        self.check_position_timeout()
        self.check_order_timeout()

    def analyze_and_trade(self, liquidation: BinanceFuturesLiquidationOrder) -> None:
        """分析清算事件并执行交易 - 使用nautilus_trader缓存API优化"""
        self.total_signals += 1
        print(f"🔍 分析信号 #{self.total_signals}")

        # 使用缓存API检查是否已有仓位
        open_positions = self.cache.positions_open(
            instrument_id=self.bybit_instrument_id,
            strategy_id=self.id
        )
        if open_positions:
            print("❌ 已有开仓位，跳过此次信号")
            self.log.debug("已有开仓位，跳过此次信号")
            return

        # 使用缓存API检查是否已有挂单
        open_orders = self.cache.orders_open(
            instrument_id=self.bybit_instrument_id,
            strategy_id=self.id
        )
        if open_orders:
            print(f"❌ 已有挂单 ({len(open_orders)}个)，跳过此次信号")
            self.log.debug("已有挂单，跳过此次信号")
            return

        # 检查价格数据是否充足 - 如果没有交易数据，跳过相关性检查
        binance_count = len(self.binance_prices)
        bybit_count = len(self.bybit_prices)
        required_window = self.config.correlation_window

        print(f"📊 价格数据检查:")
        print(f"   Binance: {binance_count} (需要: {required_window})")
        print(f"   Bybit: {bybit_count} (需要: {required_window})")

        # 如果没有足够的价格数据，使用简化的信号生成
        correlation = 0.0
        if binance_count >= required_window and bybit_count >= required_window:
            # 计算相关性
            print("🔗 计算相关性...")
            correlation = self.calculate_correlation()
            print(f"📈 相关性: {correlation:.3f} (阈值: {self.config.correlation_threshold})")

            if abs(correlation) < self.config.correlation_threshold:
                print(f"❌ 相关性不足: {abs(correlation):.3f} < {self.config.correlation_threshold}")
                self.log.debug(f"相关性不足: {correlation:.3f}")
                return
        else:
            print("⚠️  价格数据不足，跳过相关性检查，使用简化信号生成")
            # 设置一个默认的相关性值以便继续处理
            correlation = 0.5

        # 计算信号强度
        print("💪 计算信号强度...")
        signal_strength = self.calculate_signal_strength(liquidation, correlation)
        print(f"⚡ 信号强度: {signal_strength:.3f} (阈值: {self.config.signal_strength_threshold})")

        if signal_strength < self.config.signal_strength_threshold:
            print(f"❌ 信号强度不足: {signal_strength:.3f} < {self.config.signal_strength_threshold}")
            self.log.debug(f"信号强度不足: {signal_strength:.3f}")
            return

        # 确定交易方向 (反向跟随策略 - 关键修正!)
        if liquidation.order_side == OrderSide.SELL:
            # 清算卖单 -> 超卖状态 -> 在Bybit做多等待反弹
            trade_side = OrderSide.BUY
        else:
            # 清算买单 -> 超买状态 -> 在Bybit做空等待回调
            trade_side = OrderSide.SELL

        print(f"🎯 交易方向: {trade_side} (反向清算: {liquidation.order_side})")

        # 计算交易数量
        trade_quantity = self.calculate_trade_quantity(liquidation, signal_strength)
        print(f"📏 交易数量: {trade_quantity}")

        print(f"✅ 生成交易信号: {trade_side} {trade_quantity} (强度: {signal_strength:.3f})")
        self.log.info(f"生成交易信号: {trade_side} {trade_quantity} (强度: {signal_strength:.3f})")

        # 执行Maker订单
        print("📝 执行Maker订单...")
        self.execute_maker_order(trade_side, trade_quantity)

    def calculate_correlation(self) -> float:
        """计算Binance和Bybit价格的相关性"""
        try:
            # 获取最近的价格数据
            window_size = min(self.config.correlation_window,
                            len(self.binance_prices),
                            len(self.bybit_prices))

            print(f"   窗口大小: {window_size} (最小需要: 10)")

            if window_size < 10:
                print(f"   ❌ 窗口太小: {window_size} < 10")
                return 0.0

            binance_recent = list(self.binance_prices)[-window_size:]
            bybit_recent = list(self.bybit_prices)[-window_size:]

            print(f"   Binance价格范围: {min(binance_recent):.6f} - {max(binance_recent):.6f}")
            print(f"   Bybit价格范围: {min(bybit_recent):.6f} - {max(bybit_recent):.6f}")

            # 计算价格变化率
            binance_returns = np.diff(binance_recent) / np.array(binance_recent[:-1])
            bybit_returns = np.diff(bybit_recent) / np.array(bybit_recent[:-1])

            print(f"   收益率数据点: Binance={len(binance_returns)}, Bybit={len(bybit_returns)}")

            # 计算相关系数
            if len(binance_returns) > 1 and len(bybit_returns) > 1:
                correlation = np.corrcoef(binance_returns, bybit_returns)[0, 1]
                result = correlation if not np.isnan(correlation) else 0.0
                print(f"   相关系数计算结果: {result:.6f}")
                return result
            else:
                print(f"   ❌ 收益率数据不足")
                return 0.0

        except Exception as e:
            print(f"   ❌ 计算相关性时出错: {e}")
            self.log.error(f"计算相关性时出错: {e}")
            return 0.0

    def calculate_signal_strength(self, liquidation: BinanceFuturesLiquidationOrder,
                                correlation: float) -> float:
        """计算信号强度 - 简化版本，避免过度拟合"""
        # 简化的信号强度计算：主要基于清算价值
        liquidation_value = liquidation.price.as_double() * liquidation.original_quantity.as_double()

        # 基础强度：清算价值越大，信号越强
        base_strength = min(liquidation_value / 5000.0, 1.0)  # 5000 USDT为基准
        print(f"   基础强度: {base_strength:.3f} (清算价值: ${liquidation_value:.2f})")

        # 相关性加权（如果有数据）
        if abs(correlation) > 0.01:
            correlation_weight = 0.5 + 0.5 * abs(correlation)  # 0.5-1.0范围
            final_strength = base_strength * correlation_weight
            print(f"   相关性权重: {correlation_weight:.3f}")
        else:
            final_strength = base_strength
            print(f"   无相关性数据，使用基础强度")

        final_strength = min(final_strength, 1.0)
        print(f"   最终强度: {final_strength:.3f}")
        return final_strength

    def calculate_trade_quantity(self, liquidation: BinanceFuturesLiquidationOrder,
                               signal_strength: float) -> Quantity:
        """计算交易数量"""
        # 基础数量基于清算价值的比例
        liquidation_value = liquidation.price.as_double() * liquidation.original_quantity.as_double()
        base_quantity = min(liquidation_value * 0.1 / liquidation.price.as_double(),
                          float(self.config.max_trade_size))

        # 根据信号强度调整
        adjusted_quantity = base_quantity * signal_strength

        # 确保不超过最大仓位限制
        final_quantity = min(adjusted_quantity, float(self.config.max_position_size))

        return Quantity.from_str(f"{final_quantity:.0f}")

    def execute_maker_order(self, side: OrderSide, quantity: Quantity) -> None:
        """执行Maker订单 - 使用nautilus_trader内部API优化"""
        try:
            # 获取当前市价
            if not self.bybit_prices:
                print("❌ 没有Bybit价格数据，无法下单")
                self.log.warning("没有Bybit价格数据，无法下单")
                return

            current_price = self.bybit_prices[-1]
            print(f"💰 当前Bybit价格: {current_price}")

            # 计算Maker订单价格 (稍微偏离市价以确保成为Maker)
            spread_factor = self.config.maker_spread_bps / 10000.0  # 基点转换为小数
            print(f"📊 价差因子: {spread_factor:.6f} ({self.config.maker_spread_bps} bps)")

            if side == OrderSide.BUY:
                # 买单价格稍低于市价
                order_price = current_price * (1 - spread_factor)
                print(f"📉 买单价格: {current_price} * (1 - {spread_factor:.6f}) = {order_price}")
            else:
                # 卖单价格稍高于市价
                order_price = current_price * (1 + spread_factor)
                print(f"📈 卖单价格: {current_price} * (1 + {spread_factor:.6f}) = {order_price}")

            # 使用instrument的make_price方法确保价格精度正确
            adjusted_price = self.bybit_instrument.make_price(order_price)
            print(f"🔧 价格调整: {order_price} -> {adjusted_price} (精度: {self.bybit_instrument.price_increment})")

            # 使用instrument的make_qty方法确保数量精度正确
            adjusted_quantity = self.bybit_instrument.make_qty(quantity.as_double())
            print(f"📏 数量调整: {quantity} -> {adjusted_quantity} (精度: {self.bybit_instrument.size_increment})")

            # 创建限价单
            print(f"📝 创建限价单: {side.name} {adjusted_quantity} @ {adjusted_price}")
            order = self.order_factory.limit(
                instrument_id=self.bybit_instrument_id,
                order_side=side,
                quantity=adjusted_quantity,
                price=adjusted_price,
                post_only=True,
                time_in_force=TimeInForce.GTC,  # Good Till Cancel
            )

            print(f"📤 提交订单: {order.client_order_id}")

            # 提交订单
            self.submit_order(order)

            # 记录挂单信息
            self.pending_orders[str(order.client_order_id)] = {
                'order': order,
                'timestamp': self.clock.utc_now(),
                'retry_count': 0
            }

            print(f"✅ Maker订单已提交: {side.name} {adjusted_quantity} @ {adjusted_price}")
            self.log.info(f"提交Maker订单: {side.name} {adjusted_quantity} @ {adjusted_price}")

        except Exception as e:
            print(f"❌ 执行Maker订单时出错: {e}")
            self.log.error(f"执行Maker订单时出错: {e}")
            self.failed_trades += 1

    def check_order_timeout(self) -> None:
        """检查订单超时 - 使用nautilus_trader缓存API优化"""
        current_time = self.clock.utc_now()
        expired_orders = []

        # 使用缓存API获取当前策略的开放订单
        open_orders = self.cache.orders_open(
            instrument_id=self.bybit_instrument_id,
            strategy_id=self.id
        )

        # 检查挂单超时
        for order in open_orders:
            order_id = str(order.client_order_id)
            if order_id in self.pending_orders:
                order_info = self.pending_orders[order_id]
                elapsed = (current_time - order_info['timestamp']).total_seconds()

                if elapsed > self.config.order_timeout_seconds:
                    expired_orders.append((order_id, order, order_info))

        # 处理超时订单
        for order_id, order, order_info in expired_orders:
            # 取消超时订单
            self.cancel_order(order)
            print(f"⏰ 订单超时，取消: {order_id}")

            # 检查是否需要重试
            if order_info['retry_count'] < self.config.max_order_retries:
                self.log.info(f"订单超时，重试: {order_id}")
                self.retry_order(order, order_info['retry_count'] + 1)
            else:
                self.log.warning(f"订单重试次数已达上限: {order_id}")
                self.failed_trades += 1

            # 清理本地记录
            if order_id in self.pending_orders:
                del self.pending_orders[order_id]

    def retry_order(self, original_order: LimitOrder, retry_count: int) -> None:
        """重试订单 - 使用更激进的价格和nautilus_trader内部API"""
        try:
            if not self.bybit_prices:
                print("❌ 没有价格数据，无法重试订单")
                return

            current_price = self.bybit_prices[-1]

            # 更激进的价差 (减少价差以提高成交概率)
            aggressive_spread = self.config.maker_spread_bps * 0.5 / 10000.0

            if original_order.side == OrderSide.BUY:
                new_price_raw = current_price * (1 - aggressive_spread)
            else:
                new_price_raw = current_price * (1 + aggressive_spread)

            # 使用instrument的make_price方法确保价格精度正确
            new_price = self.bybit_instrument.make_price(new_price_raw)

            # 使用原订单的数量
            new_quantity = original_order.quantity

            print(f"🔄 重试订单 (第{retry_count}次): {original_order.side.name} {new_quantity} @ {new_price}")

            # 创建新订单
            new_order = self.order_factory.limit(
                instrument_id=self.bybit_instrument_id,
                order_side=original_order.side,
                quantity=new_quantity,
                price=new_price,
                post_only=True,
                time_in_force=TimeInForce.GTC,
            )

            # 提交重试订单
            self.submit_order(new_order)

            # 记录重试订单
            self.pending_orders[str(new_order.client_order_id)] = {
                'order': new_order,
                'timestamp': self.clock.utc_now(),
                'retry_count': retry_count
            }

            self.log.info(f"重试订单 (第{retry_count}次): {new_order.side.name} {new_order.quantity} @ {new_price}")
            print(f"✅ 重试订单已提交: {new_order.side.name} {new_order.quantity} @ {new_price}")

        except Exception as e:
            print(f"❌ 重试订单时出错: {e}")
            self.log.error(f"重试订单时出错: {e}")

    def check_position_timeout(self) -> None:
        """检查仓位超时和止盈止损 - 使用nautilus_trader内部API优化"""
        # 使用缓存API直接获取开仓位
        open_positions = self.cache.positions_open(
            instrument_id=self.bybit_instrument_id,
            strategy_id=self.id
        )

        if not open_positions:
            return

        position = open_positions[0]  # NETTING模式下每个交易对只有一个仓位

        # 检查止盈止损
        if self._check_take_profit_stop_loss(position):
            return  # 如果已经平仓，直接返回

        # 使用仓位的开仓时间而不是本地记录的时间
        current_time_ns = self.clock.timestamp_ns()
        elapsed_ns = current_time_ns - position.ts_opened
        elapsed_seconds = elapsed_ns / 1_000_000_000  # 转换为秒

        if elapsed_seconds > self.config.position_timeout_seconds:
            self.log.info(f"仓位超时 ({elapsed_seconds:.1f}s)，强制平仓")
            print(f"⏰ 仓位超时 ({elapsed_seconds:.1f}s)，强制平仓")
            self.close_position(position)

    def _check_take_profit_stop_loss(self, position: Position) -> bool:
        """检查止盈止损条件"""
        try:
            if not self.bybit_prices:
                return False

            current_price = self.bybit_prices[-1]

            # 安全地获取入场价格
            if hasattr(position.avg_px_open, 'as_double'):
                entry_price = position.avg_px_open.as_double()
            else:
                entry_price = float(position.avg_px_open)

            # 计算价格变化百分比
            if position.side == PositionSide.LONG:
                price_change_pct = (current_price - entry_price) / entry_price
            else:  # SHORT
                price_change_pct = (entry_price - current_price) / entry_price

            # 检查止盈
            if price_change_pct >= self.config.take_profit_pct:
                self.log.info(f"触发止盈: {price_change_pct:.4f} >= {self.config.take_profit_pct}")
                print(f"💰 触发止盈: {price_change_pct:.4f} >= {self.config.take_profit_pct}")
                self.close_position(position)
                return True

            # 检查止损
            if price_change_pct <= -self.config.stop_loss_pct:
                self.log.info(f"触发止损: {price_change_pct:.4f} <= {-self.config.stop_loss_pct}")
                print(f"🛑 触发止损: {price_change_pct:.4f} <= {-self.config.stop_loss_pct}")
                self.close_position(position)
                return True

            return False

        except Exception as e:
            self.log.error(f"检查止盈止损时出错: {e}")
            return False

    def on_order_filled(self, event) -> None:
        """订单成交时的处理 - 使用nautilus_trader内部API优化"""
        try:
            # 使用事件的直接属性而不是getattr
            if event.instrument_id != self.bybit_instrument_id:
                return

            # 移除已成交的挂单记录
            client_order_id = str(event.client_order_id)
            if client_order_id in self.pending_orders:
                del self.pending_orders[client_order_id]

            # 统计Maker/Taker成交
            from nautilus_trader.model.enums import LiquiditySide
            if event.liquidity_side == LiquiditySide.MAKER:
                self.maker_fills += 1
            else:
                self.taker_fills += 1

            # 记录成交详情
            self.log.info(
                f"订单成交: {event.order_side.name} {event.last_qty.as_double()} @ {event.last_px} ({event.liquidity_side.name})"
            )
            print(f"💰 订单成交: {event.order_side.name} {event.last_qty.as_double()} @ {event.last_px} ({event.liquidity_side.name})")

        except Exception as e:
            self.log.error(f"处理订单成交事件出错: {e}")

    def on_position_opened(self, event) -> None:
        """仓位开启时的处理 - 使用事件参数而不是Position对象"""
        if event.instrument_id == self.bybit_instrument_id:
            # 使用缓存API获取最新的仓位对象
            position = self.cache.position(event.position_id)
            if position:
                self.current_position = position
                self.position_entry_time = self.clock.utc_now()
                self.log.info(f"仓位已开启: {event.side.name} {event.quantity}")
                print(f"📈 仓位已开启: {event.side.name} {event.quantity}")

    def get_open_position(self) -> Optional[Position]:
        """使用nautilus_trader缓存API获取开仓位"""
        try:
            # 使用缓存API的positions_open方法，更高效
            open_positions = self.cache.positions_open(
                instrument_id=self.bybit_instrument_id,
                strategy_id=self.id
            )

            if open_positions:
                # 返回第一个开仓位（在NETTING模式下每个交易对只有一个仓位）
                return open_positions[0]

            return None

        except Exception as e:
            self.log.debug(f"get_open_position 读取失败: {e}")
            return None

    def on_position_closed(self, event) -> None:
        """仓位关闭时的处理 - 使用事件参数优化"""
        if event.instrument_id == self.bybit_instrument_id:
            # 直接从事件获取PnL信息
            pnl = event.realized_pnl
            if pnl and pnl.as_double() > 0:
                self.successful_trades += 1
                self.log.info(f"仓位盈利关闭: PnL = {pnl}")
                print(f"💰 仓位盈利关闭: PnL = {pnl}")
            else:
                self.failed_trades += 1
                self.log.info(f"仓位亏损关闭: PnL = {pnl}")
                print(f"📉 仓位亏损关闭: PnL = {pnl}")

            # 清理本地状态
            self.current_position = None
            self.position_entry_time = None
