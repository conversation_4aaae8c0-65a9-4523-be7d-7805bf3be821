# LEAD-LAG清算跟随策略测试结果

## 📁 核心文件
- `lead_lag_liquidation_strategy.py` - LEAD-LAG策略实现
- `lead_lag_backtest_final.py` - 回测脚本
- `liquidation_follow_strategy.py` - 原始策略（对比参考）
- `liquidation_follow_backtest.py` - 原始策略回测

## 🎯 策略概述
基于VOXEL高频印钞算法的LEAD-LAG清算跟随策略，监听Binance清算事件在Bybit执行跟随交易。

### 核心创新
- ⚡ **基差修正**: Binance价格 - 历史基差 = 公平价格
- 🔄 **高频撤单**: 100ms TTL确保价格实时跟随
- 📊 **动态定价**: delta参数根据清算价值和波动率调整
- 🎯 **信号增强**: 价格偏离度作为信号强度放大器

## ✅ 成功解决的问题

### 1. 数据订阅问题
**问题**: 策略无法收到清算事件
**解决**: 修复数据配置中的`InstrumentId`格式和`metadata`参数
```python
# 修复前
instrument_id=binance_instrument_id,

# 修复后  
instrument_id=InstrumentId.from_str(binance_instrument_id),
metadata={"instrument_id": binance_instrument_id},
```

### 2. API使用错误
**问题**: `TypeError: Argument 'position_id' has incorrect type`
**解决**: 使用正确的缓存API
```python
# 修复前
current_position = self.cache.position(self.bybit_instrument_id)

# 修复后
open_positions = self.cache.positions_open(
    instrument_id=self.bybit_instrument_id,
    strategy_id=self.id
)
```

### 3. 订单精度问题
**问题**: `quantity 223.82 invalid (precision 2 > 0)`
**解决**: 使用instrument API确保正确精度
```python
# 修复前
quantity = Quantity.from_str(f"{signal['quantity']:.2f}")

# 修复后
quantity = self.bybit_instrument.make_qty(signal['quantity'])
```

### 4. 时间类型错误
**问题**: `expire_time has incorrect type (expected datetime.datetime, got int)`
**解决**: 使用datetime对象
```python
# 修复前
expire_time=self.clock.timestamp_ns() + (self.config.ttl_ms * 1_000_000)

# 修复后
expire_time = self.clock.utc_now() + timedelta(milliseconds=self.config.ttl_ms)
```

## ❌ 当前存在的问题

### 1. POST_ONLY订单被拒绝
**现象**: `POST_ONLY LIMIT BUY order would have been a TAKER`
**原因**: 订单价格设置不当，会立即成交
**状态**: 已修复定价逻辑，添加市场价格缓冲

### 2. 清算价值阈值过高
**现象**: 大量清算事件因价值太小被过滤
**解决**: 降低阈值从$500到$50

### 3. 无成交记录
**原因**: 订单被拒绝导致无法成交
**状态**: 已添加订单状态处理逻辑

## 📊 测试数据
- **时间范围**: 2025-08-01 到 2025-08-02 (24小时)
- **Binance交易数据**: 3,612,183个TradeTick
- **Bybit交易数据**: 676,414个TradeTick  
- **清算事件**: 1,007个BinanceFuturesLiquidationOrder

## 🔧 关键配置参数
```python
min_liquidation_value_usdt=50.0,      # 最小清算价值
signal_strength_threshold=0.02,       # 信号强度阈值
correlation_threshold=0.005,          # 相关性阈值
ttl_ms=100,                          # 100ms订单生存时间
base_delta=0.002,                    # 0.2%基础价差
maker_spread_bps=5,                  # 5个基点价差
```

## 🚀 运行方式
```bash
cd /root/nautilus_trader-develop
source .venv/bin/activate
cd /root/explore/strategy
python lead_lag_backtest_final.py
```

## 📈 预期性能
- **目标胜率**: 55-70% (vs 传统策略32%)
- **Maker成交率**: >80%
- **策略类型**: 高频套利，基差修正

## 🔄 下一步优化方向
1. **优化定价算法**: 进一步改进POST_ONLY订单定价
2. **风险管理**: 添加更精细的仓位管理
3. **信号过滤**: 优化清算事件筛选逻辑
4. **性能监控**: 添加实时性能指标

## 📝 技术债务
- 需要更好的错误处理机制
- 账户报告显示需要优化
- 添加更多的性能指标统计

---
**最后更新**: 2025-08-09
**状态**: 基础功能完成，待性能优化
