import os
import argparse
import polars as pl
from pathlib import Path
from typing import List

from nautilus_trader.persistence.catalog import ParquetDataCatalog
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.objects import Price, Quantity
from nautilus_trader.model.enums import OrderSide, OrderType, TimeInForce, OrderStatus
from nautilus_trader.adapters.binance.futures.types import BinanceFuturesLiquidationOrder
from nautilus_trader.serialization.arrow.serializer import register_arrow
from nautilus_trader.model.enums import order_status_from_str
from nautilus_trader.model.enums import order_status_to_str
# Register the custom data type for catalog operations
register_arrow(
    BinanceFuturesLiquidationOrder,
    BinanceFuturesLiquidationOrder.schema(),
    BinanceFuturesLiquidationOrder.to_catalog,
    BinanceFuturesLiquidationOrder.from_catalog,
)

def ms_to_ns(ms: int) -> int:
    """Convert milliseconds to nanoseconds."""
    return ms * 1_000_000

def read_liquidation_data(data_dir: str, symbol: str) -> List[BinanceFuturesLiquidationOrder]:
    """
    Read liquidation data from Parquet files in the specified directory.

    Parameters
    ----------
    data_dir : str
        Directory containing liquidation data Parquet files
    symbol : str
        Symbol name like 'INJUSDT' (will be converted to INJUSDT-PERP.BINANCE format)

    Returns
    -------
    List[BinanceFuturesLiquidationOrder]
        List of liquidation orders sorted by timestamp
    """
    data_path = Path(data_dir)
    # Convert symbol to Binance format
    instrument_symbol = f"{symbol}-PERP.BINANCE"
    
    dfs = []
    
    # Read and concatenate all parquet files
    for file_path in data_path.glob("*.parquet"):
        df = pl.read_parquet(file_path)
        #unique item
        dfs.append(df)
    
    if not dfs:
        raise ValueError(f"No parquet files found in {data_dir}")
    
    # Concatenate all dataframes
    combined_df = pl.concat(dfs)
    #unqiue 
    combined_df = combined_df.unique()
    # Sort by timestamp
    combined_df = combined_df.sort("time")
    
    liquidation_orders = []
    
    # Iterate through rows and create liquidation orders
    for row in combined_df.iter_rows(named=True):
        #skip instrument_id not the same
        if row["instrument_id"].value != instrument_symbol:
            print(row["instrument_id"].value, instrument_symbol)
            continue
        ts_event = ms_to_ns(int(row["time"]))
        ts_init = ts_event  # Use same timestamp for initialization

        order = BinanceFuturesLiquidationOrder(
            instrument_id=InstrumentId.from_str(instrument_symbol),
            order_side=OrderSide.BUY if row["side"].upper() == "BUY" else OrderSide.SELL,
            order_type=OrderType.LIMIT if row["order_type"].upper() == "LIMIT" else OrderType.MARKET,
            time_in_force=TimeInForce.IOC if row["time_in_force"].upper() == "IOC" else TimeInForce.GTC,
            original_quantity=Quantity.from_str(str(row["original_quantity"])),
            price=Price.from_str(str(row["price"])),
            avg_price=Price.from_str(str(row["average_price"])),
            order_status=order_status_from_str(row["order_status"]),
            last_filled_quantity=Quantity.from_str(str(row["last_fill_quantity"])),
            accumulated_filled_quantity=Quantity.from_str(str(row["accumulated_fill_quantity"])),
            ts_event=ts_event,
            ts_init=ts_init,
        )
        liquidation_orders.append(order)
    
    return liquidation_orders

def read_record_liquidation_data(df) -> List[BinanceFuturesLiquidationOrder]:
    """
    Read liquidation data from Parquet files in the specified directory.
    
    Parameters
    ----------
    df: DataFrame
        data fram of record liquid data.
        
    Returns
    -------
    List[BinanceFuturesLiquidationOrder]
        List of liquidation orders sorted by timestamp
    """
    #unqiue 
    df = df.unique()
    # Sort by timestamp
    df = df.sort("time")
    
    liquidation_orders = []
    
    # Iterate through rows and create liquidation orders
    for row in df.iter_rows(named=True):        
        order = BinanceFuturesLiquidationOrder(
            instrument_id=InstrumentId.from_str(row["instrument_id"]),
            order_side=OrderSide.BUY if row["order_side"].upper() == "BUY" else OrderSide.SELL,
            order_type=OrderType.LIMIT if row["order_type"].upper() == "LIMIT" else OrderType.MARKET,
            time_in_force=TimeInForce.IOC if row["time_in_force"].upper() == "IOC" else TimeInForce.GTC,
            original_quantity=Quantity.from_str(str(row["original_quantity"])),
            price=Price.from_str(str(row["price"])),
            avg_price=Price.from_str(str(row["avg_price"])),
            order_status=order_status_from_str(row["order_status"]),
            last_filled_quantity=Quantity.from_str(str(row[" last_filled_quantity"])),
            accumulated_filled_quantity=Quantity.from_str(str(row["accumulated_filled_quantity"])),
            ts_event= row["ts_event"],
            ts_init=row["ts_init"],
        )
        liquidation_orders.append(order)
    
    return liquidation_orders


def main():
    """Process liquidation data and write to Parquet catalog using command-line arguments."""
    parser = argparse.ArgumentParser(description="Process liquidation data and write to Nautilus Trader Parquet catalog.")
    parser.add_argument(
        "--catalog-dir",
        type=str,
        default="../examples/catalog",
        help="Path to the Nautilus Trader data catalog directory.",
    )
    parser.add_argument(
        "--data-dir",
        type=str,
        required=True,
        help="Path to the directory containing the liquidation data Parquet files.",
    )
    parser.add_argument(
        "--symbol",
        type=str,
        required=True,
        help="Symbol name like 'INJUSDT', 'WLDUSDT' (without -PERP.BINANCE suffix).",
    )

    args = parser.parse_args()

    # Initialize catalog
    catalog_path = Path(args.catalog_dir).resolve()
    print(f"Using catalog at: {catalog_path}")
    catalog = ParquetDataCatalog(str(catalog_path))

    # Process liquidation data file by file
    data_dir_path = Path(args.data_dir).resolve()
    print(f"Processing liquidation data for {args.symbol} from {data_dir_path}...")

    # Find parquet files
    parquet_files = list(data_dir_path.glob("*.parquet"))

    if not parquet_files:
        print(f"No parquet files found in {data_dir_path}")
        return

    print(f"Found {len(parquet_files)} files to process")
    total_orders = 0
    instrument_symbol = f"{args.symbol}-PERP.BINANCE"

    # Process each file separately
    for file_idx, file_path in enumerate(parquet_files, 1):
        print(f"Processing file {file_idx}/{len(parquet_files)}: {file_path.name}")

        try:
            # Read parquet file
            df = pl.read_parquet(file_path)

            # Filter for the specific instrument and remove duplicates
            df = df.filter(pl.col("instrument_id") == instrument_symbol)
            df = df.unique()
            df = df.sort("ts_event")

            if len(df) == 0:
                print(f"  - No data for {instrument_symbol} in this file")
                continue

            # Convert to liquidation orders
            liquidation_orders = []
            for row in df.iter_rows(named=True):
                ts_event = row["ts_event"]  # Already integer in nanoseconds
                ts_init = row["ts_init"]    # Already integer in nanoseconds

                order = BinanceFuturesLiquidationOrder(
                    instrument_id=InstrumentId.from_str(instrument_symbol),
                    order_side=OrderSide.BUY if row["order_side"] == "BUY" else OrderSide.SELL,
                    order_type=OrderType.LIMIT if row["order_type"] == "LIMIT" else OrderType.MARKET,
                    time_in_force=TimeInForce.IOC if row["time_in_force"] == "IOC" else TimeInForce.GTC,
                    original_quantity=Quantity.from_str(str(row["original_quantity"])),
                    price=Price.from_str(str(row["price"])),
                    avg_price=Price.from_str(str(row["avg_price"])),
                    order_status=order_status_from_str(row["order_status"]),
                    last_filled_quantity=Quantity.from_str(str(row["last_filled_quantity"])),
                    accumulated_filled_quantity=Quantity.from_str(str(row["accumulated_filled_quantity"])),
                    ts_event=ts_event,
                    ts_init=ts_init,
                )
                liquidation_orders.append(order)

            # Write to catalog
            if liquidation_orders:
                catalog.write_data(liquidation_orders)
                total_orders += len(liquidation_orders)
                print(f"  - Wrote {len(liquidation_orders)} liquidation orders")

        except Exception as e:
            print(f"  - Error processing {file_path.name}: {e}")
            continue

    print(f"\n✓ Total processed: {total_orders} liquidation orders")
    print(f"✓ Data written to catalog at '{catalog_path}'")

if __name__ == "__main__":
    main()
