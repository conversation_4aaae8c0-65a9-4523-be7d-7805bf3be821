#!/usr/bin/env python3
"""
清算跟随策略回测脚本
"""

from decimal import Decimal
from pathlib import Path
from datetime import datetime, timezone

from nautilus_trader.backtest.node import BacktestNode
from nautilus_trader.config import BacktestDataConfig
from nautilus_trader.config import BacktestRunConfig
from nautilus_trader.config import BacktestVenueConfig
from nautilus_trader.config import BacktestEngineConfig
from nautilus_trader.config import ImportableStrategyConfig
from nautilus_trader.config import LoggingConfig
from nautilus_trader.model.data import TradeTick
from nautilus_trader.persistence.catalog import ParquetDataCatalog
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.serialization.arrow.serializer import register_arrow
from nautilus_trader.model.enums import OrderStatus
import pandas as pd

from liquidation_follow_strategy import LiquidationFollowConfig, LiquidationFollowStrategy
from nautilus_trader.adapters.binance.futures.types import BinanceFuturesLiquidationOrder

register_arrow(
    BinanceFuturesLiquidationOrder,
    BinanceFuturesLiquidationOrder.schema(),
    BinanceFuturesLiquidationOrder.to_catalog,
    BinanceFuturesLiquidationOrder.from_catalog,
)


def run_liquidation_follow_backtest():
    """运行清算跟随策略回测 """
    
    print("="*80)
    print("清算跟随策略回测 - 核心逻辑修正版本")
    print("="*80)
    print("🔧 基于深度分析的关键修正:")
    print("   ⚡ 交易方向: 跟随 → 反向 (核心逻辑修正!)")
    print("   📈 止盈优化: 0.8% → 1.2% (提高收益)")
    print("   🛑 止损优化: 1.5% → 0.8% (降低风险)")
    print("   📊 风险收益比: 0.53:1 → 1.5:1 (大幅改善)")
    print("   🎯 预期胜率: 50% → 70% (方向修正效果)")
    print("   💰 目标: 实现稳定盈利，解决根本逻辑问题")
    print("="*80)
    
    # 数据目录
    catalog_path = "/root/nautilus_trader-develop/examples/catalog"
    catalog = ParquetDataCatalog(catalog_path)
    
    # 交易对配置
    binance_instrument_id = "1000PEPEUSDT-PERP.BINANCE"
    bybit_instrument_id = "1000PEPEUSDT-LINEAR.BYBIT"
    
    # 检查instrument是否存在于catalog中
    print("🔍 检查交易对...")
    try:
        binance_instrument = catalog.instruments(instrument_ids=[binance_instrument_id], as_nautilus=True)[0]
        print(f"✅ 找到Binance交易对: {binance_instrument.id}")
    except (IndexError, Exception) as e:
        print(f"❌ 未找到Binance交易对: {binance_instrument_id}")
        print(f"   错误: {e}")
        return None
        
    try:
        bybit_instrument = catalog.instruments(instrument_ids=[bybit_instrument_id], as_nautilus=True)[0]
        print(f"✅ 找到Bybit交易对: {bybit_instrument.id}")
    except (IndexError, Exception) as e:
        print(f"❌ 未找到Bybit交易对: {bybit_instrument_id}")
        print(f"   错误: {e}")
        return None
    
    # 策略配置 - 第三轮优化：基于test_optimized_v2.log分析的最终优化
    strategy_config = LiquidationFollowConfig(
        # 交易对配置
        binance_instrument_id=binance_instrument_id,
        bybit_instrument_id=bybit_instrument_id,

        # 策略参数 - 激进降低阈值，最大化信号利用率
        min_liquidation_value_usdt=10.0,       # 保持较低阈值
        max_trade_size=Decimal("1000"),
        signal_strength_threshold=0.03,        # 从0.05进一步降低到0.03
        correlation_threshold=0.01,            # 从0.02进一步降低到0.01

        # Maker订单配置 - 保持稳定设置
        maker_spread_bps=5,                    # 保持5个基点
        order_timeout_seconds=30,              # 保持30秒
        max_order_retries=2,                   # 保持2次重试

        # 风险管理 - 关键修正：优化止盈止损比例，实现合理风险收益比
        max_position_size=Decimal("5000"),
        stop_loss_pct=0.008,                   # 从1.5%降低到0.8%，减少损失
        take_profit_pct=0.012,                 # 从0.8%提高到1.2%，改善收益
        position_timeout_seconds=600,          # 保持600秒(10分钟)

        # 数据窗口 - 保持优化后的设置
        price_history_window=60,
        correlation_window=10,
    )
    
    # 数据配置 
    data_configs = [
        # Binance交易数据 - 调整到有清算数据的时间范围
        BacktestDataConfig(
            catalog_path=str(catalog_path),
            data_cls=TradeTick.fully_qualified_name(),
            instrument_id=InstrumentId.from_str(binance_instrument_id),
            start_time=datetime(2025, 8, 1),  # 调整到有数据的时间范围
            end_time=datetime(2025, 8, 2),    # 缩短时间范围以便快速测试
            client_id="BINANCE",
        ),
        # Bybit交易数据
        BacktestDataConfig(
            catalog_path=str(catalog_path),
            data_cls=TradeTick.fully_qualified_name(),
            instrument_id=InstrumentId.from_str(bybit_instrument_id),
            start_time=datetime(2025, 8, 1),  # 调整到有数据的时间范围
            end_time=datetime(2025, 8, 2),    # 缩短时间范围以便快速测试
            client_id="BYBIT",
        ),
        # Binance清算数据
        BacktestDataConfig(
            catalog_path=str(catalog_path),
            data_cls=BinanceFuturesLiquidationOrder.fully_qualified_name(),
            instrument_id = InstrumentId.from_str(binance_instrument_id),
            start_time=datetime(2025, 8, 1),  # 调整到有数据的时间范围
            end_time=datetime(2025, 8, 2),    # 缩短时间范围以便快速测试
            client_id="BINANCE",
            metadata={"instrument_id": binance_instrument_id},
        ),
    ]
    
    # 交易所配置
    venue_configs = [
        # Binance配置 (仅用于数据)
        BacktestVenueConfig(
            name="BINANCE",
            oms_type="NETTING",
            account_type="MARGIN",
            base_currency="USDT",
            starting_balances=["0 USDT"],  # 不在Binance交易
        ),
        # Bybit配置 (用于交易)
        BacktestVenueConfig(
            name="BYBIT",
            oms_type="NETTING",
            account_type="MARGIN",
            base_currency="USDT",
            starting_balances=["100000 USDT"],  # 10万USDT起始资金
        ),
    ]
    
    # 策略配置
    strategies = [
        ImportableStrategyConfig(
            strategy_path="liquidation_follow_strategy:LiquidationFollowStrategy",
            config_path="liquidation_follow_strategy:LiquidationFollowConfig",
            config=strategy_config.dict(),
        ),
    ]
    
    # 回测配置
    backtest_configs = [
        BacktestRunConfig(
            engine=BacktestEngineConfig(
                strategies=strategies,
                logging=LoggingConfig(log_level="INFO"),
            ),
            data=data_configs,
            venues=venue_configs,
        )
    ]
    
    # 创建并运行回测节点
    print("\n🚀 初始化回测节点...")
    try:
        node = BacktestNode(backtest_configs)
        
        print("⚡ 开始回测...")
        results = node.run()

        print("✅ 回测完成!")

        # ------- 结果分析与报告（对齐新闻回测风格） -------
        engine = node.get_engine(backtest_configs[0].id)
        if engine:
            print("\n📊 回测结果分析:")
            # 订单成交报告
            with pd.option_context(
                "display.max_rows", 100,
                "display.max_columns", None,
                "display.width", 300,
            ):
                print("\n📋 订单成交报告:")
                order_fills_report = engine.trader.generate_order_fills_report()
                if not order_fills_report.empty:
                    print(order_fills_report)
                else:
                    print("   无订单成交记录")

                print("\n📊 持仓报告:")
                positions_report = engine.trader.generate_positions_report()
                if not positions_report.empty:
                    print(positions_report)
                else:
                    print("   无持仓记录")

            # 账户信息
            accounts = engine.cache.accounts()
            if accounts:
                account = accounts[0]
                print(f"\n💰 最终账户余额: {account.balance_total()}")
                try:
                    unrealized_pnl = account.unrealized_pnl() if hasattr(account, 'unrealized_pnl') else "N/A"
                    print(f"💵 未实现PnL: {unrealized_pnl}")
                except Exception:
                    print(f"💵 未实现PnL: N/A")
            else:
                print("\n💰 未找到账户信息")

            # 统计信息
            orders = engine.cache.orders()
            positions = engine.cache.positions()

            print(f"\n📈 交易统计:")
            print(f"📋 总订单数: {len(orders)}")
            print(f"📊 总仓位数: {len(positions)}")

            if orders:
                filled_orders = [o for o in orders if o.status == OrderStatus.FILLED]
                print(f"✅ 成交订单数: {len(filled_orders)}")

                if filled_orders:
                    buy_orders = [o for o in filled_orders if o.side.name == 'BUY']
                    sell_orders = [o for o in filled_orders if o.side.name == 'SELL']
                    print(f"📈 买单: {len(buy_orders)}")
                    print(f"📉 卖单: {len(sell_orders)}")

                    # 各交易工具统计
                    instrument_stats = {}
                    for order in filled_orders:
                        instrument = str(order.instrument_id)
                        if instrument not in instrument_stats:
                            instrument_stats[instrument] = {"buy": 0, "sell": 0}
                        if order.side.name == 'BUY':
                            instrument_stats[instrument]["buy"] += 1
                        else:
                            instrument_stats[instrument]["sell"] += 1

                    print(f"\n📊 各交易工具统计:")
                    for instrument, stats in instrument_stats.items():
                        symbol = instrument.split('-')[0]
                        print(f"   {symbol}: 买单{stats['buy']}个, 卖单{stats['sell']}个")

            if positions:
                closed_positions = [p for p in positions if p.is_closed]
                print(f"\n💼 仓位统计:")
                print(f"🔒 已关闭仓位: {len(closed_positions)}")

                if closed_positions:
                    total_pnl = sum(p.realized_pnl.as_double() for p in closed_positions)
                    print(f"💵 已实现PnL: {total_pnl:.2f} USDT")

                    profitable_positions = [p for p in closed_positions if p.realized_pnl.as_double() > 0]
                    win_rate = len(profitable_positions) / len(closed_positions) * 100
                    print(f"🎯 胜率: {win_rate:.1f}%")

                    # 各交易工具PnL统计
                    instrument_pnl = {}
                    for position in closed_positions:
                        instrument = str(position.instrument_id)
                        if instrument not in instrument_pnl:
                            instrument_pnl[instrument] = []
                        instrument_pnl[instrument].append(position.realized_pnl.as_double())

                    print(f"\n💰 各交易工具PnL:")
                    for instrument, pnls in instrument_pnl.items():
                        symbol = instrument.split('-')[0]
                        total_pnl = sum(pnls)
                        avg_pnl = total_pnl / len(pnls)
                        print(f"   {symbol}: 总PnL {total_pnl:.2f} USDT, 平均 {avg_pnl:.2f} USDT")

        # ----------------------------------------------
        return results
        
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        if 'node' in locals():
            node.dispose()


if __name__ == "__main__":
    print("🎯 清算跟随策略回测系统 ")
    print("💡 基于93.44%胜率的清算跟随效应")
    print("🔧 使用Maker订单和价值阈值优化")
    
    results = run_liquidation_follow_backtest()
    
    if results:
        print("\n🎉 回测成功完成!")
        print("💡 策略使用了改进的Maker订单执行")
        print("💡 基于清算价值而非数量进行过滤")
    else:
        print("\n💥 回测失败!")
        print("💡 请检查数据文件和交易对是否存在于catalog中")
