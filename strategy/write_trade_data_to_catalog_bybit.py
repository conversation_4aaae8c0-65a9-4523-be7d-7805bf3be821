#!/usr/bin/env python3
# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2024 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------

"""
Enhanced script to read trade data from gzipped CSV files and write it to a Nautilus Trader Parquet catalog.
This version can automatically fetch instrument specifications from Bybit API to create instruments.

Assumes input CSV files are named like: <INSTRUMENT_SYMBOL>_YYYY-MM-DD.csv.gz
(e.g., BTCUSDT-LINEAR_2025-04-08.csv.gz)

Required CSV columns: timestamp, price, volume, side, id

Usage:

1. Using symbol to auto-fetch from Bybit API (RECOMMENDED):
   python write_trade_data_to_catalog_bybit.py --symbol 1000PEPEUSDT --data-dir /root/explore/data/1000PEPEUSDT/trades --catalog-dir /root/nautilus_trader-develop/examples/catalog
   python write_trade_data_to_catalog_bybit.py --symbol BTCUSDT --data-dir . --catalog-dir ../catalog
   python write_trade_data_to_catalog_bybit.py --symbol INJUSDT --data-dir data/INJUSDT/trades --catalog-dir ../examples/catalog
   python write_trade_data_to_catalog_bybit.py --symbol XAUTUSDT --data-dir data/XAUTUSDT/trades --catalog-dir ../examples/catalog

2. Using pre-defined instrument ID (legacy method):
   python write_trade_data_to_catalog_bybit.py --instrument-id 'BTCUSDT-LINEAR.BYBIT' --data-dir output/BTCPERP-LINEAR/trades --catalog-dir catalog

The script will automatically fetch instrument specifications from Bybit API and create the instrument when using --symbol.
"""

import argparse
import os
import glob
import polars as pl
import pandas as pd
import requests
import time
from pathlib import Path
from decimal import Decimal
from typing import List, Dict, Any, Optional

from nautilus_trader.model.data import TradeTick
from nautilus_trader.model.identifiers import InstrumentId, TradeId
from nautilus_trader.model.objects import Price, Quantity
from nautilus_trader.model.enums import AggressorSide
from nautilus_trader.persistence.catalog import ParquetDataCatalog
from nautilus_trader.model.instruments import CryptoPerpetual
from nautilus_trader.model.identifiers import Symbol
from nautilus_trader.model.identifiers import TradeId
from nautilus_trader.model.identifiers import Venue
from nautilus_trader.model.currencies import BTC
from nautilus_trader.model.currencies import ETH
from nautilus_trader.model.currencies import USDT
from nautilus_trader.model.objects import Currency
from nautilus_trader.model.objects import Money
from nautilus_trader.model.enums import CurrencyType


def fetch_bybit_instrument_info(symbol: str, category: str = "linear") -> Optional[Dict[str, Any]]:
    """
    Fetch instrument information from Bybit API.

    Parameters
    ----------
    symbol : str
        Symbol name like 'BTCUSDT'
    category : str
        Product type: 'spot', 'linear', 'inverse', 'option'

    Returns
    -------
    Optional[Dict[str, Any]]
        Instrument information from Bybit API or None if not found
    """
    url = "https://api.bybit.com/v5/market/instruments-info"
    params = {
        "category": category,
        "symbol": symbol
    }

    try:
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        if data.get("retCode") == 0 and data.get("result", {}).get("list"):
            return data["result"]["list"][0]
        else:
            print(f"No instrument found for {symbol} in category {category}")
            return None

    except Exception as e:
        print(f"Error fetching instrument info for {symbol}: {e}")
        return None


def get_currency_from_code(code: str) -> Currency:
    """
    Get Currency object from currency code.

    Parameters
    ----------
    code : str
        Currency code like 'BTC', 'USDT', 'XAUT', etc.

    Returns
    -------
    Currency
        Currency object
    """
    # Common predefined currencies
    common_currencies = {
        'BTC': BTC,
        'ETH': ETH,
        'USDT': USDT,
    }

    if code in common_currencies:
        return common_currencies[code]

    # Create custom currency for others
    return Currency(
        code=code,
        precision=8,  # Default precision
        iso4217=0,
        name=code,
        currency_type=CurrencyType.CRYPTO,
    )


def create_instrument_from_bybit_api(symbol: str, category: str = "linear") -> Optional[CryptoPerpetual]:
    """
    Create a CryptoPerpetual instrument from Bybit API data.

    Parameters
    ----------
    symbol : str
        Symbol name like 'BTCUSDT'
    category : str
        Product type, default 'linear'

    Returns
    -------
    Optional[CryptoPerpetual]
        Created instrument or None if failed
    """
    instrument_info = fetch_bybit_instrument_info(symbol, category)
    if not instrument_info:
        return None

    try:
        # Extract basic info
        base_coin = instrument_info["baseCoin"]
        quote_coin = instrument_info["quoteCoin"]
        settle_coin = instrument_info["settleCoin"]

        # Get currencies
        base_currency = get_currency_from_code(base_coin)
        quote_currency = get_currency_from_code(quote_coin)
        settlement_currency = get_currency_from_code(settle_coin)

        # Extract price and size filters
        price_filter = instrument_info["priceFilter"]
        lot_size_filter = instrument_info["lotSizeFilter"]

        # Calculate precision from tick size and qty step
        tick_size_str = price_filter["tickSize"]
        qty_step_str = lot_size_filter["qtyStep"]

        # Calculate precision by counting decimal places
        if '.' in tick_size_str:
            price_precision = len(tick_size_str.split('.')[-1])
        else:
            price_precision = 0

        if '.' in qty_step_str:
            size_precision = len(qty_step_str.split('.')[-1])
        else:
            size_precision = 0

        # Create Price and Quantity objects with correct precision
        price_increment = Price(float(price_filter["tickSize"]), precision=price_precision)
        size_increment = Quantity(float(lot_size_filter["qtyStep"]), precision=size_precision)
        max_quantity = Quantity(float(lot_size_filter["maxOrderQty"]), precision=size_precision)
        min_quantity = Quantity(float(lot_size_filter["minOrderQty"]), precision=size_precision)
        max_price = Price(float(price_filter["maxPrice"]), precision=price_precision)
        min_price = Price(float(price_filter["minPrice"]), precision=price_precision)

        # Create instrument
        instrument = CryptoPerpetual(
            instrument_id=InstrumentId(
                symbol=Symbol(f"{symbol}-LINEAR"),
                venue=Venue("BYBIT"),
            ),
            raw_symbol=Symbol(symbol),
            base_currency=base_currency,
            quote_currency=quote_currency,
            settlement_currency=settlement_currency,
            is_inverse=False,  # Linear contracts are not inverse
            price_precision=price_precision,
            price_increment=price_increment,
            size_precision=size_precision,
            size_increment=size_increment,
            max_quantity=max_quantity,
            min_quantity=min_quantity,
            max_notional=None,
            min_notional=Money(float(lot_size_filter["minNotionalValue"]), settlement_currency),
            max_price=max_price,
            min_price=min_price,
            margin_init=Decimal("0.0500"),  # Default values, could be fetched from API
            margin_maint=Decimal("0.0250"),
            maker_fee=Decimal("0.000550"),  # Default values, could be fetched from API
            taker_fee=Decimal("0.000200"),
            ts_event=int(time.time() * 1e9),  # Current timestamp
            ts_init=int(time.time() * 1e9),
        )

        print(f"Successfully created instrument for {symbol}")
        print(f"  Price precision: {price_precision}, tick size: {tick_size_str}")
        print(f"  Size precision: {size_precision}, qty step: {qty_step_str}")
        print(f"  Min quantity: {lot_size_filter['minOrderQty']}")
        print(f"  Max quantity: {lot_size_filter['maxOrderQty']}")
        print(f"  Min notional: {lot_size_filter['minNotionalValue']} {settle_coin}")

        return instrument

    except Exception as e:
        print(f"Error creating instrument from API data: {e}")
        return None


def btcusdt_linear_bybit() -> CryptoPerpetual:
    """
    Return the ByBit Linear BTCUSDT instrument for backtesting.

    Returns
    -------
    CryptoPerpetual

    """
    return CryptoPerpetual(
        instrument_id=InstrumentId(
            symbol=Symbol("BTCUSDT-LINEAR"),
            venue=Venue("BYBIT"),
        ),
        raw_symbol=Symbol("BTCUSDT"),
        base_currency=BTC,
        quote_currency=USDT,
        settlement_currency=USDT,
        is_inverse=False,
        price_precision=2,
        price_increment=Price.from_str("0.01"),
        size_precision=6,
        size_increment=Quantity.from_str("0.000001"),
        max_quantity=Quantity.from_str("100000.000000"),
        min_quantity=Quantity.from_str("0.000001"),
        max_notional=None,
        min_notional=Money(100.00, USDT),
        max_price=Price.from_str("4529764.00"),
        min_price=Price.from_str("556.80"),
        margin_init=Decimal("0.0500"),
        margin_maint=Decimal("0.0250"),
        maker_fee=Decimal("0.000550"),
        taker_fee=Decimal("0.000200"),
        ts_event=1742212811176999936,
        ts_init=1742215166205355675,
    )


def xautusdt_linear_bybit() -> CryptoPerpetual:
    """
    Return the ByBit Linear XAUTUSDT instrument for backtesting.

    Returns
    -------
    CryptoPerpetual

    """
    return CryptoPerpetual(
        instrument_id=InstrumentId(
            symbol=Symbol("XAUTUSDT-LINEAR"),
            venue=Venue("BYBIT"),
        ),
        raw_symbol=Symbol("XAUTUSDT"),
        base_currency=Currency(
            code="XAUT",
            precision=2,
            iso4217=0,
            name="Tether Gold",
            currency_type=CurrencyType.CRYPTO,
        ),
        quote_currency=USDT,
        settlement_currency=USDT,
        is_inverse=False,
        price_precision=2,
        price_increment=Price.from_str("0.01"),
        size_precision=3,
        size_increment=Quantity.from_str("0.001"),
        max_quantity=Quantity.from_str("36.000"),
        min_quantity=Quantity.from_str("0.001"),
        max_notional=None,
        min_notional=Money(5.00, USDT),
        max_price=Price.from_str("199999.98"),
        min_price=Price.from_str("0.01"),
        margin_init=Decimal("0.0500"),
        margin_maint=Decimal("0.0250"),
        maker_fee=Decimal("0.000550"),  
        taker_fee=Decimal("0.000200"),
        ts_event=1742212811176999936,
        ts_init=1742215166205355675,
    )

def paxgusdt_linear_bybit() -> CryptoPerpetual:
    """
    Return the ByBit Linear PAXGUSDT instrument for backtesting.

    Returns
    -------
    CryptoPerpetual

    """
    return CryptoPerpetual(
        instrument_id=InstrumentId(
            symbol=Symbol("PAXGUSDT-LINEAR"),
            venue=Venue("BYBIT"),
        ),
        raw_symbol=Symbol("PAXGUSDT"),
        base_currency=Currency(
            code="PAXG",
            precision=2,
            iso4217=0,
            name="PAX Gold",
            currency_type=CurrencyType.CRYPTO,
        ),
        quote_currency=USDT,
        settlement_currency=USDT,
        is_inverse=False,
        price_precision=2,
        price_increment=Price.from_str("0.01"),
        size_precision=4,   
        size_increment=Quantity.from_str("0.0001"),
        max_quantity=Quantity.from_str("140.0000"),
        min_quantity=Quantity.from_str("0.0001"),
        max_notional=None,
        min_notional=Money(5.00, USDT),
        max_price=Price.from_str("199999.98"),
        min_price=Price.from_str("0.01"),
        margin_init=Decimal("0.0500"),
        margin_maint=Decimal("0.0250"),
        maker_fee=Decimal("0.000550"),
        taker_fee=Decimal("0.000200"),
        ts_event=1742212811176999936,
        ts_init=1742215166205355675,
    )

def process_trade_data_by_file(
    data_dir: str | Path,
    instrument_id: InstrumentId,
    catalog: ParquetDataCatalog,
    price_precision: int,
    size_precision: int,
) -> int:
    """
    Process trade data from gzipped CSV files one by one.

    Parameters
    ----------
    data_dir : str | Path
        Directory containing trade data gzipped CSV files.
    instrument_id : InstrumentId
        The InstrumentId for the trade ticks.
    catalog : ParquetDataCatalog
        Catalog to write data to.
    price_precision : int
        Precision of the price.
    size_precision : int
        Precision of the size.

    Returns
    -------
    int
        Total number of trades processed.

    Raises
    ------
    ValueError
        If no gzipped CSV files are found in the specified directory.
    """
    data_path = Path(data_dir)
    instrument_prefix = instrument_id.symbol.value.split("-")[0]

    # Find matching CSV.gz files
    file_pattern = f"{instrument_prefix}*.csv.gz"
    print(f"Searching for files matching pattern: {file_pattern} in {data_path}")

    found_files = list(data_path.glob(file_pattern))
    print(f"Found {len(found_files)} files to process")

    if not found_files:
        raise ValueError(f"No valid gzipped CSV files matching pattern '{file_pattern}' found in {data_dir}")

    total_trades = 0

    # Process each file separately
    for file_idx, file_path in enumerate(found_files, 1):
        print(f"Processing file {file_idx}/{len(found_files)}: {file_path.name}")

        try:
            # Read CSV using pandas
            df_pd = pd.read_csv(file_path, compression='gzip')

            # Ensure required columns exist
            required_cols = {'timestamp', 'price', 'size', 'side', 'trdMatchID'}
            if not required_cols.issubset(df_pd.columns):
                print(f"Warning: Skipping file {file_path} due to missing columns. Found: {df_pd.columns}")
                continue


            # Filter out invalid trades
            initial_count = len(df_pd)
            df_pd = df_pd[(df_pd["price"] > 0) & (df_pd["size"] > 0)]
            filtered_count = len(df_pd)
            if initial_count > filtered_count:
                print(f"  - Filtered out {initial_count - filtered_count} invalid trades")

            # Sort by timestamp
            df_pd = df_pd.sort_values('timestamp')

            # Convert to trade ticks
            trade_ticks = []
            for _, row in df_pd.iterrows():
                ts_event = int(row["timestamp"]) * 1_000_000_000  # second to nanoseconds
                ts_init = ts_event
                aggressor_side = AggressorSide.BUYER if row["side"].lower() == "buy" else AggressorSide.SELLER

                trade = TradeTick(
                    instrument_id=instrument_id,
                    price=Price(float(row["price"]), precision=price_precision),
                    size=Quantity(float(row['size']), precision=size_precision),
                    aggressor_side=aggressor_side,
                    trade_id=TradeId(str(row['trdMatchID'])),
                    ts_event=ts_event,
                    ts_init=ts_init,
                )
                trade_ticks.append(trade)

            # Write to catalog immediately
            if trade_ticks:
                catalog.write_data(trade_ticks)
                total_trades += len(trade_ticks)
                print(f"  - Wrote {len(trade_ticks)} trades to catalog")

        except Exception as e:
            print(f"Warning: Could not process file {file_path}: {e}")
            continue

    return total_trades


def main() -> None:
    """Process trade data and write to Parquet catalog using command-line arguments."""
    parser = argparse.ArgumentParser(description="Process trade data and write to Nautilus Trader Parquet catalog.")
    parser.add_argument(
        "--catalog-dir",
        type=str,
        default="/root/nautilus_trader-develop/examples/catalog", # Default catalog path
        help="Path to the Nautilus Trader data catalog directory.",
    )
    parser.add_argument(
        "--data-dir",
        type=str,
        required=True, # Data directory is required
        help="Path to the directory containing the input trade data Parquet files.",
    )
    parser.add_argument(
        "--instrument-id",
        type=str,
        help="Instrument ID string (e.g., 'BTCUSDT-LINEAR.BYBIT'). If not provided, will use --symbol to auto-create.",
    )
    parser.add_argument(
        "--symbol",
        type=str,
        help="Symbol name like 'BTCUSDT', 'INJUSDT' (without -LINEAR suffix). Will auto-fetch specs from Bybit API.",
    )
    parser.add_argument(
        "--category",
        type=str,
        default="linear",
        help="Bybit product category: 'spot', 'linear', 'inverse', 'option'. Default: 'linear'",
    )

    args = parser.parse_args()

    # Validate arguments
    if not args.instrument_id and not args.symbol:
        print("Error: Either --instrument-id or --symbol must be provided.")
        return

    # Initialize catalog
    catalog_path = Path(args.catalog_dir).resolve()
    print(f"Using catalog at: {catalog_path}")
    catalog = ParquetDataCatalog(str(catalog_path))

    # Determine instrument
    if args.symbol:
        # Auto-create instrument from Bybit API
        symbol = args.symbol
        category = args.category

        print(f"Fetching instrument specifications for {symbol} from Bybit API...")
        instrument = create_instrument_from_bybit_api(symbol, category)

        if not instrument:
            print(f"Failed to create instrument for {symbol}. Exiting.")
            return

        instrument_id_str = instrument.id.value
        print(f"Created instrument: {instrument_id_str}")

        # Register instrument in catalog
        print(f"Registering instrument {instrument_id_str} in catalog...")
        catalog.write_data([instrument])

    else:
        # Use provided instrument ID
        instrument_id_str = args.instrument_id
        instrument_id_obj = InstrumentId.from_str(instrument_id_str)

        # Fetch instrument details from catalog
        print(f"Looking for instrument '{instrument_id_str}' in catalog...")
        try:
            # Ensure the instrument exists in the catalog
            instrument = catalog.instruments(instrument_ids=[instrument_id_str], as_nautilus=True)[0]
            print(f"Found instrument: {instrument.id}")
        except IndexError:
            print(f"Error: Instrument ID '{instrument_id_str}' not found in catalog '{catalog_path}'.")
            print("Please ensure the instrument is registered in the catalog before running this script.")
            # Example registration (if needed, uncomment and adapt):
            default_instrument = btcusdt_linear_bybit()
            if instrument_id_str == default_instrument.id.value:
                print(f"Registering default instrument {instrument_id_str}...")
                catalog.write_data([default_instrument])
                instrument = default_instrument
            else:
                return # Exit if a non-default, non-existent instrument is requested

    # Process trade data file by file
    data_dir_path = Path(args.data_dir).resolve()
    print(f"Processing trade data for {instrument_id_str} from {data_dir_path}...")

    # Find CSV.gz files
    instrument_prefix = instrument.id.symbol.value.split("-")[0]
    file_pattern = f"{instrument_prefix}*.csv.gz"
    found_files = list(data_dir_path.glob(file_pattern))

    if not found_files:
        print(f"No CSV.gz files matching pattern '{file_pattern}' found in {data_dir_path}")
        return

    print(f"Found {len(found_files)} files to process")
    total_trades = 0

    # Process each file separately
    for file_idx, file_path in enumerate(found_files, 1):
        print(f"Processing file {file_idx}/{len(found_files)}: {file_path.name}")

        try:
            # Read CSV file
            df_pd = pd.read_csv(file_path, compression='gzip')

            # Check required columns
            required_cols = {'timestamp', 'price', 'size', 'side', 'trdMatchID'}
            if not required_cols.issubset(df_pd.columns):
                print(f"  - Skipping: missing columns. Found: {df_pd.columns}")
                continue

            # Filter and sort
            initial_count = len(df_pd)
            df_pd = df_pd[(df_pd["price"] > 0) & (df_pd["size"] > 0)]
            df_pd = df_pd.sort_values('timestamp')

            # Convert to trade ticks
            trade_ticks = []
            for _, row in df_pd.iterrows():
                ts_event = int(row["timestamp"]) * 1_000_000_000
                aggressor_side = AggressorSide.BUYER if row["side"].lower() == "buy" else AggressorSide.SELLER

                trade = TradeTick(
                    instrument_id=instrument.id,
                    price=Price(float(row["price"]), precision=instrument.price_precision),
                    size=Quantity(float(row['size']), precision=instrument.size_precision),
                    aggressor_side=aggressor_side,
                    trade_id=TradeId(str(row['trdMatchID'])),
                    ts_event=ts_event,
                    ts_init=ts_event,
                )
                trade_ticks.append(trade)

            # Write to catalog
            if trade_ticks:
                catalog.write_data(trade_ticks)
                total_trades += len(trade_ticks)
                print(f"  - Wrote {len(trade_ticks)} trades")

        except Exception as e:
            print(f"  - Error processing {file_path.name}: {e}")
            continue

    print(f"\n✓ Total processed: {total_trades} trades")
    print(f"✓ Data written to catalog at '{catalog_path}'")


if __name__ == "__main__":
    main()