import pyarrow.ipc as ipc
import pyarrow as pa
import polars as pl 
import os
import re
from typing import List, Optional
from nautilus_trader.serialization.arrow.serializer import ArrowSerializer
from nautilus_trader.adapters.binance.futures.types import BinanceFuturesLiquidationOrder
from nautilus_trader.serialization.arrow.serializer import register_arrow
register_arrow(
    BinanceFuturesLiquidationOrder,
    BinanceFuturesLiquidationOrder.schema(),
    BinanceFuturesLiquidationOrder.to_catalog,
    BinanceFuturesLiquidationOrder.from_catalog,
)
def extract_timestamp_from_filename(filename: str) -> Optional[int]:
    """
    Extract timestamp from a filename that matches the pattern 'custom_binance_futures_liquidation_order_TIMESTAMP.feather'.
    
    Parameters
    ----------
    filename : str
        File name to extract timestamp from
        
    Returns
    -------
    Optional[int]
        Extracted timestamp as integer, or None if no timestamp found
    """
    pattern = r'custom_binance_futures_liquidation_order_(\d+)\.feather'
    match = re.search(pattern, os.path.basename(filename))
    if match:
        return int(match.group(1))
    return None

def filter_files_by_timestamp(file_list: List[str], min_timestamp: Optional[int] = None) -> List[str]:
    """
    Filter files based on their embedded timestamp values.
    
    Parameters
    ----------
    file_list : List[str]
        List of file paths to filter
    min_timestamp : Optional[int]
        Minimum timestamp to include (inclusive), or None to include all files
        
    Returns
    -------
    List[str]
        Filtered list of file paths
    """
    if min_timestamp is None:
        return file_list
        
    filtered_files = []
    for file in file_list:
        timestamp = extract_timestamp_from_filename(file)
        if timestamp is not None and timestamp >= min_timestamp:
            filtered_files.append(file)
            
    return filtered_files

def read_and_merge_feathers(
    file_list: List[str],
    data_cls: BinanceFuturesLiquidationOrder,
    filter_symbol_name: str,
    timestamp_col: str = "ts_event",
    min_timestamp: Optional[int] = None,
) -> pl.DataFrame:
    """
    Read multiple feather files and convert to a DataFrame.
    Files can be filtered by their embedded timestamps.
    
    Parameters
    ----------
    file_list : List[str]
        List of feather file paths
    data_cls : BinanceFuturesLiquidationOrder
        Data class type
    filter_symbol_name : str
        Trading pair name to filter
    timestamp_col : str
        Column name for timestamp
    min_timestamp : Optional[int]
        Minimum timestamp to include (inclusive), or None to include all files
    
    Returns
    -------
    pl.DataFrame
        Sorted DataFrame containing merged data
    """
    # Filter files by timestamp if needed
    filtered_files = filter_files_by_timestamp(file_list, min_timestamp)
    
    if not filtered_files:
        print(f"No files found with timestamp >= {min_timestamp}")
        return pl.DataFrame()
        
    print(f"Processing {len(filtered_files)}/{len(file_list)} files after timestamp filtering")
    
    # Read all filtered files
    datas = []
    for file in filtered_files:
        with open(file, 'rb') as source:
            reader = ipc.open_stream(source)
            table = reader.read_all()
            data = ArrowSerializer.deserialize(data_cls=data_cls, batch=table)
            for item in data:
                if item.instrument_id.value == filter_symbol_name:
                    datas.append(BinanceFuturesLiquidationOrder.to_dict(item))
    
    if not datas:
        return pl.DataFrame()
    
    # Convert list of dictionaries to DataFrame
    df = pl.DataFrame(datas)
    return df.sort(timestamp_col)


# Usage example
if __name__ == "__main__":
    # Prepare file list
    import glob 
    files = glob.glob("../data/custom_*feather")
    
    # Set minimum timestamp
    min_timestamp = 1739605949838121773
    
    # Read and merge files with timestamp filtering
    df = read_and_merge_feathers(
        files,
        BinanceFuturesLiquidationOrder,
        filter_symbol_name="1000PEPEUSDT-PERP.BINANCE",
        min_timestamp=min_timestamp
    )
    
    print(f"Extracted {df.height} records")
    df.write_parquet("1000PEPEUSDT_filtered_liquid.parquet")