#!/usr/bin/env python3
# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2024 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------
'''
python write_trade_data_to_catalog_binance.py --symbol 1000PEPEUSDC --data-dir /root/explore/data/1000PEPEUSDC/trades --catalog-dir /root/nautilus_trader-develop/examples/catalog 
'''
import os
import argparse
import glob
import polars as pl
from pathlib import Path
from typing import List, Iterator
from decimal import Decimal

from nautilus_trader.model.data import TradeTick
from nautilus_trader.model.identifiers import InstrumentId, TradeId
from nautilus_trader.model.objects import Price, Quantity
from nautilus_trader.model.enums import AggressorSide
from nautilus_trader.persistence.catalog import ParquetDataCatalog
from nautilus_trader.model.instruments import CryptoPerpetual
from nautilus_trader.model.identifiers import Symbol
from nautilus_trader.model.identifiers import TradeId
from nautilus_trader.model.identifiers import Venue
from nautilus_trader.model.currencies import BTC
from nautilus_trader.model.currencies import ETH
from nautilus_trader.model.currencies import USDT
from nautilus_trader.model.objects import Currency
from nautilus_trader.model.objects import Money
from nautilus_trader.model.enums import CurrencyType

def ms_to_ns(ms: int) -> int:
    """
    Convert milliseconds to nanoseconds.
    
    Parameters
    ----------
    ms : int
        Time in milliseconds
        
    Returns
    -------
    int
        Time in nanoseconds
    """
    return ms * 1_000_000


def read_trade_data(data_dir: str | Path, symbol: str, price_precision: int, size_precision: int) -> List[TradeTick]:
    """
    Read and parse trade data from CSV files in the specified directory.

    Parameters
    ----------
    data_dir : str | Path
        Directory containing trade data CSV files in CSV format
        Expected columns: time, price, qty, is_buyer_maker, id
    symbol : str
        Symbol name like 'INJUSDT' (will be converted to INJUSDT-PERP.BINANCE format)
    price_precision : int
        Precision of the price
    size_precision : int
        Precision of the size

    Returns
    -------
    List[TradeTick]
        List of trade ticks sorted chronologically by timestamp

    Raises
    ------
    ValueError
        If no CSV files are found in the specified directory
    """
    data_path = Path(data_dir)
    # Convert symbol to Binance format
    instrument_id = f"{symbol}-PERP.BINANCE"
    
    dfs = []
    
    # Read and concatenate all CSV files
    for file_path in data_path.glob("*.parquet"):
        df = pl.read_parquet(file_path)
        dfs.append(df)
    
    if not dfs:
        raise ValueError(f"No CSV files found in {data_dir}")
    
    # Concatenate all dataframes
    combined_df = pl.concat(dfs)
    # Sort by timestamp
    combined_df = combined_df.sort("time")
    
    trade_ticks = []
    
    # Iterate through rows and create trade ticks
    for row in combined_df.iter_rows(named=True):
        ts_event = ms_to_ns(int(row["time"]))
        ts_init = ts_event  # Use same timestamp for initialization
        
        aggressor_side = AggressorSide.BUYER if row["is_buyer_maker"] else AggressorSide.SELLER
        
        trade = TradeTick(
            instrument_id=InstrumentId.from_str(instrument_id),
            price=Price(float(row["price"]),precision=price_precision),
            size=Quantity(float(row["qty"]),precision=size_precision),
            aggressor_side=aggressor_side,
            trade_id=TradeId(str(row["id"])),
            ts_event=ts_event,
            ts_init=ts_init,
        )
        trade_ticks.append(trade)
    
    return trade_ticks


def main() -> None:
    """Process trade data and write to Parquet catalog using command-line arguments."""
    parser = argparse.ArgumentParser(description="Process trade data and write to Nautilus Trader Parquet catalog.")
    parser.add_argument(
        "--catalog-dir",
        type=str,
        default="../examples/catalog",
        help="Path to the Nautilus Trader data catalog directory.",
    )
    parser.add_argument(
        "--data-dir",
        type=str,
        required=True,
        help="Path to the directory containing the trade data Parquet files.",
    )
    parser.add_argument(
        "--symbol",
        type=str,
        required=True,
        help="Symbol name like 'INJUSDT', 'WLDUSDT' (without -PERP.BINANCE suffix).",
    )

    args = parser.parse_args()

    # Initialize catalog
    catalog_path = Path(args.catalog_dir).resolve()
    print(f"Using catalog at: {catalog_path}")
    catalog = ParquetDataCatalog(str(catalog_path))

    # Get instrument from catalog
    instrument_id = f"{args.symbol}-PERP.BINANCE"
    print(f"Looking for instrument '{instrument_id}' in catalog...")

    try:
        instrument = catalog.instruments(instrument_ids=[instrument_id], as_nautilus=True)[0]
        print(f"Found instrument: {instrument.id}")
    except IndexError:
        print(f"Error: Instrument ID '{instrument_id}' not found in catalog '{catalog_path}'.")
        print("Please ensure the instrument is registered in the catalog before running this script.")
    #create usdc instruemnt 
    if args.symbol.endswith("USDC"):
        usdc_instrument =  CryptoPerpetual(
            instrument_id=InstrumentId(
                symbol=Symbol(f"{args.symbol}-PERP.BINANCE"),
                venue=Venue("BINANCE"),
            ),
            raw_symbol=Symbol(args.symbol),
            base_currency=instrument.base_currency,
            quote_currency=instrument.quote_currency,
            settlement_currency=instrument.settlement_currency,
            is_inverse=False,  # Linear contracts are not inverse
            price_precision=instrument.price_precision,
            price_increment=instrument.price_increment,
            size_precision=instrument.size_precision,
            size_increment=instrument.size_increment,
            max_quantity=instrument.max_quantity,
            min_quantity=instrument.min_quantity,
            max_notional=instrument.max_notional,
            min_notional=instrument.min_notional,
            max_price=instrument.max_price,
            min_price=instrument.min_price,
            margin_init=instrument.margin_init,
            margin_maint=instrument.margin_maint,
            maker_fee=Decimal("0.000000"), 
            taker_fee=Decimal("0.000400"),
            ts_event=instrument.ts_event,
            ts_init=instrument.ts_event
        )
        instruemnt = usdc_instrument
        catalog.write_data([usdc_instrument])
    # Process trade data file by file
    data_dir_path = Path(args.data_dir).resolve()
    print(f"Processing trade data for {args.symbol} from {data_dir_path}...")

    # Find parquet files
    parquet_files = list(data_dir_path.glob("*.parquet"))

    if not parquet_files:
        print(f"No parquet files found in {data_dir_path}")
        return

    print(f"Found {len(parquet_files)} files to process")
    total_trades = 0
    instrument_id = f"{args.symbol}-PERP.BINANCE"

    # Process each file separately
    for file_idx, file_path in enumerate(parquet_files, 1):
        print(f"Processing file {file_idx}/{len(parquet_files)}: {file_path.name}")

        try:
            # Read parquet file
            df = pl.read_parquet(file_path)
            df = df.sort("time")

            # Convert to trade ticks
            trade_ticks = []
            for row in df.iter_rows(named=True):
                ts_event = ms_to_ns(int(row["time"]))
                aggressor_side = AggressorSide.BUYER if row["is_buyer_maker"] else AggressorSide.SELLER
                trade = TradeTick(
                    instrument_id=InstrumentId.from_str(instrument_id),
                    price=Price(float(row["price"]), precision=instrument.price_precision),
                    size=Quantity(float(row["qty"]), precision=instrument.size_precision),
                    aggressor_side=aggressor_side,
                    trade_id=TradeId(str(row["id"])),
                    ts_event=ts_event,
                    ts_init=ts_event,
                )
                trade_ticks.append(trade)

            # Write to catalog
            if trade_ticks:
                catalog.write_data(trade_ticks)
                total_trades += len(trade_ticks)
                print(f"  - Wrote {len(trade_ticks)} trades")

        except Exception as e:
            print(f"  - Error processing {file_path.name}: {e}")
            continue

    print(f"\n✓ Total processed: {total_trades} trades")
    print(f"✓ Data written to catalog at '{catalog_path}'")



if __name__ == "__main__":
    main()